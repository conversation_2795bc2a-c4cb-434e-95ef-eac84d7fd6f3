from sqlalchemy import Column, Integer, String, DateTime, Text, func
from sqlalchemy.dialects.mysql import TINYINT
from app.database import Base

class NewsInfo(Base):
    __tablename__ = "news_info"

    id = Column(Integer, primary_key=True, index=True)
    title = Column(String(255), nullable=False, comment="标题")
    description = Column(String(500), comment="描述")
    content = Column(Text, comment="内容")
    read_count = Column(String(20), default="0", comment="阅读量")
    comments = Column(String(20), default="0", comment="评论数")
    publish_date = Column(String(20), comment="发布日期")
    author = Column(String(64), comment="作者")
    category = Column(String(64), comment="分类")
    image = Column(String(255), comment="图片")
    create_time = Column(DateTime, default=func.now(), comment="创建时间")
    update_time = Column(DateTime, default=func.now(), onupdate=func.now(), comment="更新时间")
    deleted = Column(TINYINT, default=0, comment="是否删除")
