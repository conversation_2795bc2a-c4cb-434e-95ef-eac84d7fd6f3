import request from '@/utils/request'

// 新闻相关接口
export function getNewsList(params?: any) {
  return request({
    url: '/news/',
    method: 'get',
    params
  })
}

export function getNews(id: number) {
  return request({
    url: `/news/${id}`,
    method: 'get'
  })
}

export function createNews(data: any) {
  return request({
    url: '/news/',
    method: 'post',
    data
  })
}

export function updateNews(id: number, data: any) {
  return request({
    url: `/news/${id}`,
    method: 'put',
    data
  })
}

export function deleteNews(id: number) {
  return request({
    url: `/news/${id}`,
    method: 'delete'
  })
}
