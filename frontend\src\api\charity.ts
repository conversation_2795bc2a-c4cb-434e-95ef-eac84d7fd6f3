import request from '@/utils/request'

// 公益活动相关接口
export function getCharities(params?: any) {
  return request({
    url: '/charity/',
    method: 'get',
    params
  })
}

export function getCharity(id: number) {
  return request({
    url: `/charity/${id}`,
    method: 'get'
  })
}

export function createCharity(data: any) {
  return request({
    url: '/charity/',
    method: 'post',
    data
  })
}

export function updateCharity(id: number, data: any) {
  return request({
    url: `/charity/${id}`,
    method: 'put',
    data
  })
}

export function deleteCharity(id: number) {
  return request({
    url: `/charity/${id}`,
    method: 'delete'
  })
}

// 公益活动分类相关接口
export function getCharityCategories(params?: any) {
  return request({
    url: '/charity/categories/',
    method: 'get',
    params
  })
}

export function getCharityCategory(id: number) {
  return request({
    url: `/charity/categories/${id}`,
    method: 'get'
  })
}

export function createCharityCategory(data: any) {
  return request({
    url: '/charity/categories/',
    method: 'post',
    data
  })
}

export function updateCharityCategory(id: number, data: any) {
  return request({
    url: `/charity/categories/${id}`,
    method: 'put',
    data
  })
}

export function deleteCharityCategory(id: number) {
  return request({
    url: `/charity/categories/${id}`,
    method: 'delete'
  })
}
