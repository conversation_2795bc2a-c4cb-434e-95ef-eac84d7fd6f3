import axios from 'axios'

const service = axios.create({
  baseURL: import.meta.env.VITE_APP_BASE_API,
  timeout: 5000 
})

// Request interceptor
service.interceptors.request.use(
  config => {
    // Add token to headers for authentication
    const token = localStorage.getItem('token') || ''
    if (token) {
      config.headers['Authorization'] = 'Bearer ' + token
    }
    return config
  },
  error => {
    console.log(error) // for debug
    return Promise.reject(error)
  }
)

// Response interceptor
service.interceptors.response.use(
  response => {
    const res = response.data
    // 对于FastAPI返回的数据，直接返回
    if (response.status === 200) {
      return res
    }
    // if the custom code is not 20000, it is judged as an error.
    if (res.code && res.code !== 20000) {
        // You can add some global error handling here
      return Promise.reject(new Error(res.message || 'Error'))
    } else {
      return res
    }
  },
  error => {
    console.log('err' + error) // for debug
    // You can add some global error handling here
    return Promise.reject(error)
  }
)

export default service