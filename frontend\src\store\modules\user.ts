import { defineStore } from 'pinia'
import { login } from '@/api/user'
import { getToken, setToken, removeToken } from '@/utils/auth'
import { ElMessage } from 'element-plus'

export const useUserStore = defineStore('user', {
  state: () => ({
    token: getToken() || localStorage.getItem('token') || undefined,
    username: localStorage.getItem('username') || '',
    isLoggedIn: !!(getToken() || localStorage.getItem('token'))
  }),
  actions: {
    // user login
    login(userInfo: { username: string, password: string }) {
      const { username, password } = userInfo
      return new Promise((resolve, reject) => {
        login({ username: username.trim(), password: password })
          .then((response: any) => {
            const { access_token } = response
            this.token = access_token
            this.username = username
            this.isLoggedIn = true
            
            // 同时保存到cookie和localStorage
            setToken(access_token)
            localStorage.setItem('token', access_token)
            localStorage.setItem('username', username)
            
            resolve(response)
          })
          .catch(error => {
            console.error('Login failed:', error)
            reject(error)
          })
      })
    },

    // user logout
    logout() {
        this.token = undefined
        this.username = ''
        this.isLoggedIn = false
        
        // 清除所有存储的认证信息
        removeToken()
        localStorage.removeItem('token')
        localStorage.removeItem('username')
        
        // 提示用户
        ElMessage.success('已成功退出登录')
        
        // 重置路由并跳转到登录页
        window.location.href = '/login'
    }
  }
})