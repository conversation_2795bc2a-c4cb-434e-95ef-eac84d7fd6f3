<template>
  <div class="news-form">
    <el-card class="box-card">
      <template #header>
        <div class="card-header">
          <span>{{ isEdit ? '编辑新闻' : '发布新闻' }}</span>
          <el-button @click="goBack">返回列表</el-button>
        </div>
      </template>

      <el-form
        ref="formRef"
        :model="form"
        :rules="rules"
        label-width="120px"
        class="news-form-content"
      >
        <el-row :gutter="20">
          <el-col :span="16">
            <el-form-item label="新闻标题" prop="title">
              <el-input v-model="form.title" placeholder="请输入新闻标题" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="新闻分类" prop="category">
              <el-select v-model="form.category" placeholder="请选择分类" style="width: 100%">
                <el-option label="环保资讯" value="1" />
                <el-option label="科技新闻" value="2" />
                <el-option label="法律法规" value="3" />
                <el-option label="安全知识" value="4" />
                <el-option label="文化教育" value="5" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="作者" prop="author">
              <el-input v-model="form.author" placeholder="请输入作者姓名" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="发布日期" prop="publish_date">
              <el-date-picker
                v-model="publishDate"
                type="date"
                placeholder="选择发布日期"
                style="width: 100%"
                @change="handleDateChange"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="评论数" prop="comments">
              <el-input-number v-model="form.comments" :min="0" style="width: 100%" />
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item label="新闻图片" prop="image">
          <el-input v-model="form.image" placeholder="请输入图片URL">
            <template #append>
              <el-button @click="previewImage" :disabled="!form.image">预览</el-button>
            </template>
          </el-input>
        </el-form-item>

        <el-form-item label="新闻描述" prop="description">
          <el-input
            v-model="form.description"
            type="textarea"
            :rows="3"
            placeholder="请输入新闻简要描述"
            maxlength="500"
            show-word-limit
          />
        </el-form-item>

        <el-form-item label="新闻内容" prop="content">
          <el-input
            v-model="form.content"
            type="textarea"
            :rows="10"
            placeholder="请输入详细的新闻内容，支持HTML格式"
          />
        </el-form-item>

        <el-form-item>
          <el-button type="primary" @click="handleSubmit" :loading="loading">
            {{ isEdit ? '更新' : '发布' }}
          </el-button>
          <el-button @click="handleReset">重置</el-button>
          <el-button @click="goBack">取消</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 图片预览对话框 -->
    <el-dialog v-model="imagePreviewVisible" title="图片预览" width="50%">
      <div class="image-preview">
        <el-image
          :src="form.image"
          style="width: 100%; max-height: 400px"
          fit="contain"
          :preview-src-list="[form.image]"
        />
      </div>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { ElMessage } from 'element-plus'
import type { FormInstance } from 'element-plus'
import { createNews, updateNews, getNews } from '@/api/news'

const router = useRouter()
const route = useRoute()
const formRef = ref<FormInstance>()

// 响应式数据
const loading = ref(false)
const isEdit = ref(false)
const newsId = ref<number | null>(null)
const imagePreviewVisible = ref(false)
const publishDate = ref('')

// 表单数据
const form = reactive({
  title: '',
  category: '',
  author: '',
  publish_date: '',
  comments: 0,
  image: '',
  description: '',
  content: ''
})

// 表单验证规则
const rules = {
  title: [
    { required: true, message: '请输入新闻标题', trigger: 'blur' },
    { min: 5, max: 100, message: '标题长度在 5 到 100 个字符', trigger: 'blur' }
  ],
  category: [
    { required: true, message: '请选择新闻分类', trigger: 'change' }
  ],
  author: [
    { required: true, message: '请输入作者姓名', trigger: 'blur' }
  ],
  publish_date: [
    { required: true, message: '请选择发布日期', trigger: 'change' }
  ],
  description: [
    { required: true, message: '请输入新闻描述', trigger: 'blur' },
    { max: 500, message: '描述不能超过500个字符', trigger: 'blur' }
  ],
  content: [
    { required: true, message: '请输入新闻内容', trigger: 'blur' }
  ]
}

// 初始化数据
const initData = async () => {
  const id = route.query.id
  if (id) {
    isEdit.value = true
    newsId.value = Number(id)
    await fetchNewsData(newsId.value)
  } else {
    // 设置默认发布日期为今天
    const today = new Date()
    publishDate.value = today.toISOString().split('T')[0]
    form.publish_date = today.toISOString().split('T')[0]
  }
}

// 获取新闻数据
const fetchNewsData = async (id: number) => {
  try {
    const response = await getNews(id)
    Object.keys(form).forEach(key => {
      if (response[key] !== undefined) {
        form[key] = response[key]
      }
    })
    
    // 设置日期选择器的值
    if (form.publish_date) {
      publishDate.value = form.publish_date
    }
  } catch (error) {
    ElMessage.error('获取新闻数据失败')
    console.error(error)
  }
}

// 处理日期变化
const handleDateChange = (date: string) => {
  form.publish_date = date
}

// 预览图片
const previewImage = () => {
  if (form.image) {
    imagePreviewVisible.value = true
  }
}

// 提交表单
const handleSubmit = async () => {
  if (!formRef.value) return
  
  try {
    await formRef.value.validate()
    loading.value = true
    
    // 处理评论数，确保是字符串
    const submitData = {
      ...form,
      comments: String(form.comments)
    }
    
    if (isEdit.value && newsId.value) {
      await updateNews(newsId.value, submitData)
      ElMessage.success('更新成功')
    } else {
      await createNews(submitData)
      ElMessage.success('发布成功')
    }
    
    goBack()
  } catch (error) {
    if (error !== false) { // 不是表单验证错误
      ElMessage.error(isEdit.value ? '更新失败' : '发布失败')
      console.error(error)
    }
  } finally {
    loading.value = false
  }
}

// 重置表单
const handleReset = () => {
  if (!formRef.value) return
  formRef.value.resetFields()
  publishDate.value = ''
}

// 返回列表
const goBack = () => {
  router.push('/news/list')
}

// 初始化
onMounted(() => {
  initData()
})
</script>

<style scoped>
.news-form {
  padding: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.news-form-content {
  max-width: 800px;
}

.image-preview {
  text-align: center;
}
</style>
