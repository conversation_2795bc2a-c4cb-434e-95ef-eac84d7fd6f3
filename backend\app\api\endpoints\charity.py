from typing import List
from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session
from app import crud, schemas
from app.database import get_db

router = APIRouter()

# 公益活动相关接口
@router.get("/", response_model=List[schemas.Charity])
def get_charities(
    skip: int = 0,
    limit: int = 100,
    category: str = None,
    status: str = None,
    db: Session = Depends(get_db)
):
    """
    获取公益活动列表
    """
    if category:
        charities = crud.charity.get_charities_by_category(db, category=category, skip=skip, limit=limit)
    elif status:
        charities = crud.charity.get_charities_by_status(db, status=status, skip=skip, limit=limit)
    else:
        charities = crud.charity.get_charities(db, skip=skip, limit=limit)
    return charities

@router.get("/{charity_id}", response_model=schemas.Charity)
def get_charity(charity_id: int, db: Session = Depends(get_db)):
    """
    获取单个公益活动详情
    """
    charity = crud.charity.get_charity(db, charity_id=charity_id)
    if not charity:
        raise HTTPException(status_code=404, detail="公益活动不存在")
    return charity

@router.post("/", response_model=schemas.Charity)
def create_charity(charity: schemas.CharityCreate, db: Session = Depends(get_db)):
    """
    创建公益活动
    """
    return crud.charity.create_charity(db=db, charity=charity)

@router.put("/{charity_id}", response_model=schemas.Charity)
def update_charity(
    charity_id: int,
    charity: schemas.CharityUpdate,
    db: Session = Depends(get_db)
):
    """
    更新公益活动
    """
    db_charity = crud.charity.update_charity(db=db, charity_id=charity_id, charity=charity)
    if not db_charity:
        raise HTTPException(status_code=404, detail="公益活动不存在")
    return db_charity

@router.delete("/{charity_id}")
def delete_charity(charity_id: int, db: Session = Depends(get_db)):
    """
    删除公益活动
    """
    success = crud.charity.delete_charity(db=db, charity_id=charity_id)
    if not success:
        raise HTTPException(status_code=404, detail="公益活动不存在")
    return {"message": "删除成功"}

# 公益活动分类相关接口
@router.get("/categories/", response_model=List[schemas.CharityCategory])
def get_charity_categories(
    skip: int = 0,
    limit: int = 100,
    db: Session = Depends(get_db)
):
    """
    获取公益活动分类列表
    """
    return crud.charity.get_charity_categories(db, skip=skip, limit=limit)

@router.get("/categories/{category_id}", response_model=schemas.CharityCategory)
def get_charity_category(category_id: int, db: Session = Depends(get_db)):
    """
    获取单个公益活动分类详情
    """
    category = crud.charity.get_charity_category(db, category_id=category_id)
    if not category:
        raise HTTPException(status_code=404, detail="分类不存在")
    return category

@router.post("/categories/", response_model=schemas.CharityCategory)
def create_charity_category(category: schemas.CharityCategoryCreate, db: Session = Depends(get_db)):
    """
    创建公益活动分类
    """
    return crud.charity.create_charity_category(db=db, category=category)

@router.put("/categories/{category_id}", response_model=schemas.CharityCategory)
def update_charity_category(
    category_id: int,
    category: schemas.CharityCategoryUpdate,
    db: Session = Depends(get_db)
):
    """
    更新公益活动分类
    """
    db_category = crud.charity.update_charity_category(db=db, category_id=category_id, category=category)
    if not db_category:
        raise HTTPException(status_code=404, detail="分类不存在")
    return db_category

@router.delete("/categories/{category_id}")
def delete_charity_category(category_id: int, db: Session = Depends(get_db)):
    """
    删除公益活动分类
    """
    success = crud.charity.delete_charity_category(db=db, category_id=category_id)
    if not success:
        raise HTTPException(status_code=404, detail="分类不存在")
    return {"message": "删除成功"}
