from pydantic import BaseModel
from typing import Optional, List, Any
from datetime import datetime

# 题目基础模式
class QuestionBase(BaseModel):
    type: str  # single, multiple, judge
    category: Optional[str] = None
    question: str
    options: Optional[List[dict]] = None
    correct_answer: str
    explanation: Optional[str] = None

# 创建题目
class QuestionCreate(QuestionBase):
    pass

# 更新题目
class QuestionUpdate(QuestionBase):
    type: Optional[str] = None
    question: Optional[str] = None
    correct_answer: Optional[str] = None

# 数据库中的题目
class QuestionInDBBase(QuestionBase):
    id: int
    create_time: Optional[datetime] = None
    update_time: Optional[datetime] = None

    class Config:
        from_attributes = True

# 返回给客户端的题目
class Question(QuestionInDBBase):
    pass

# 题目分类基础模式
class CategoryBase(BaseModel):
    name: str

# 创建题目分类
class CategoryCreate(CategoryBase):
    pass

# 更新题目分类
class CategoryUpdate(CategoryBase):
    name: Optional[str] = None

# 数据库中的题目分类
class CategoryInDBBase(CategoryBase):
    id: int
    create_time: Optional[datetime] = None
    update_time: Optional[datetime] = None

    class Config:
        from_attributes = True

# 返回给客户端的题目分类
class Category(CategoryInDBBase):
    pass

# 答题历史基础模式
class QuizHistoryBase(BaseModel):
    open_id: str
    score: Optional[int] = 0
    total: Optional[int] = 0
    mode: Optional[str] = "practice"
    mode_name: Optional[str] = None
    timestamp: Optional[int] = None
    time_str: Optional[str] = None

# 创建答题历史
class QuizHistoryCreate(QuizHistoryBase):
    pass

# 数据库中的答题历史
class QuizHistoryInDBBase(QuizHistoryBase):
    id: int
    create_time: Optional[datetime] = None
    update_time: Optional[datetime] = None

    class Config:
        from_attributes = True

# 返回给客户端的答题历史
class QuizHistory(QuizHistoryInDBBase):
    pass
