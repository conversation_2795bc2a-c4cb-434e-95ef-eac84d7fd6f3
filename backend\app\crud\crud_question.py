from typing import List, Optional
from sqlalchemy.orm import Session
from app.models.question import Question, Category, QuizHistory
from app.schemas.question import QuestionCreate, QuestionUpdate, CategoryCreate, CategoryUpdate, QuizHistoryCreate

# 题目CRUD操作
def get_question(db: Session, question_id: int) -> Optional[Question]:
    return db.query(Question).filter(Question.id == question_id, Question.deleted == 0).first()

def get_questions(db: Session, skip: int = 0, limit: int = 100) -> List[Question]:
    return db.query(Question).filter(Question.deleted == 0).offset(skip).limit(limit).all()

def get_questions_by_category(db: Session, category: str, skip: int = 0, limit: int = 100) -> List[Question]:
    return db.query(Question).filter(Question.category == category, Question.deleted == 0).offset(skip).limit(limit).all()

def get_questions_by_type(db: Session, question_type: str, skip: int = 0, limit: int = 100) -> List[Question]:
    return db.query(Question).filter(Question.type == question_type, Question.deleted == 0).offset(skip).limit(limit).all()

def create_question(db: Session, question: QuestionCreate) -> Question:
    db_question = Question(**question.dict())
    db.add(db_question)
    db.commit()
    db.refresh(db_question)
    return db_question

def update_question(db: Session, question_id: int, question: QuestionUpdate) -> Optional[Question]:
    db_question = get_question(db, question_id)
    if db_question:
        update_data = question.dict(exclude_unset=True)
        for field, value in update_data.items():
            setattr(db_question, field, value)
        db.commit()
        db.refresh(db_question)
    return db_question

def delete_question(db: Session, question_id: int) -> bool:
    db_question = get_question(db, question_id)
    if db_question:
        db_question.deleted = 1
        db.commit()
        return True
    return False

# 题目分类CRUD操作
def get_category(db: Session, category_id: int) -> Optional[Category]:
    return db.query(Category).filter(Category.id == category_id, Category.deleted == 0).first()

def get_categories(db: Session, skip: int = 0, limit: int = 100) -> List[Category]:
    return db.query(Category).filter(Category.deleted == 0).offset(skip).limit(limit).all()

def create_category(db: Session, category: CategoryCreate) -> Category:
    db_category = Category(**category.dict())
    db.add(db_category)
    db.commit()
    db.refresh(db_category)
    return db_category

def update_category(db: Session, category_id: int, category: CategoryUpdate) -> Optional[Category]:
    db_category = get_category(db, category_id)
    if db_category:
        update_data = category.dict(exclude_unset=True)
        for field, value in update_data.items():
            setattr(db_category, field, value)
        db.commit()
        db.refresh(db_category)
    return db_category

def delete_category(db: Session, category_id: int) -> bool:
    db_category = get_category(db, category_id)
    if db_category:
        db_category.deleted = 1
        db.commit()
        return True
    return False

# 答题历史CRUD操作
def get_quiz_history(db: Session, history_id: int) -> Optional[QuizHistory]:
    return db.query(QuizHistory).filter(QuizHistory.id == history_id, QuizHistory.deleted == 0).first()

def get_quiz_histories(db: Session, skip: int = 0, limit: int = 100) -> List[QuizHistory]:
    return db.query(QuizHistory).filter(QuizHistory.deleted == 0).offset(skip).limit(limit).all()

def get_quiz_histories_by_user(db: Session, open_id: str, skip: int = 0, limit: int = 100) -> List[QuizHistory]:
    return db.query(QuizHistory).filter(QuizHistory.open_id == open_id, QuizHistory.deleted == 0).offset(skip).limit(limit).all()

def create_quiz_history(db: Session, quiz_history: QuizHistoryCreate) -> QuizHistory:
    db_quiz_history = QuizHistory(**quiz_history.dict())
    db.add(db_quiz_history)
    db.commit()
    db.refresh(db_quiz_history)
    return db_quiz_history
