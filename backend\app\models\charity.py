from sqlalchemy import Column, Integer, String, DateTime, Text, func
from sqlalchemy.dialects.mysql import TINYINT
from app.database import Base

class Charity(Base):
    __tablename__ = "charity"

    id = Column(Integer, primary_key=True, index=True)
    title = Column(String(255), nullable=False, comment="标题")
    description = Column(String(500), comment="描述")
    content = Column(Text, comment="内容")
    time = Column(String(64), comment="活动时间")
    location = Column(String(255), comment="活动地点")
    participants = Column(Integer, default=0, comment="当前参与人数")
    max_participants = Column(Integer, default=0, comment="最大参与人数")
    image = Column(String(255), comment="图片")
    status = Column(String(20), default="upcoming", comment="状态（ongoing-进行中，upcoming-即将开始，finished-已结束）")
    category = Column(String(50), default="环保", comment="活动分类")
    organizer = Column(String(64), comment="组织者")
    contact = Column(String(64), comment="联系方式")
    requirements = Column(Text, comment="参与要求")
    notices = Column(Text, comment="活动须知")
    create_time = Column(DateTime, default=func.now(), comment="创建时间")
    update_time = Column(DateTime, default=func.now(), onupdate=func.now(), comment="更新时间")
    deleted = Column(TINYINT, default=0, comment="是否删除")

class CharityCategory(Base):
    __tablename__ = "charity_category"

    id = Column(Integer, primary_key=True, index=True)
    name = Column(String(64), nullable=False, comment="分类名称")
    create_time = Column(DateTime, default=func.now(), comment="创建时间")
    update_time = Column(DateTime, default=func.now(), onupdate=func.now(), comment="更新时间")
    deleted = Column(TINYINT, default=0, comment="是否删除")
