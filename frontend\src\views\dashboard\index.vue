<template>
  <div class="dashboard-container">
    <el-card class="welcome-card">
      <template #header>
        <div class="card-header">
          <h3>欢迎使用 Lun-B 公益活动管理系统</h3>
          <el-button @click="refreshData" :loading="loading">
            <el-icon><Refresh /></el-icon>
            刷新数据
          </el-button>
        </div>
      </template>
      <div v-if="loading" class="dashboard-content">
        <p>加载中...</p>
      </div>
      <div v-else class="dashboard-content">
        <p>当前登录用户: <strong>{{ userStore.username }}</strong></p>
        <p>登录状态: <el-tag type="success" v-if="userStore.isLoggedIn">已登录</el-tag></p>
        <p>系统时间: {{ currentTime }}</p>
        <p>这是一个基于 Vue 3 + TypeScript + Element Plus + FastAPI 的公益活动管理平台</p>
      </div>
    </el-card>

    <!-- 核心统计数据 -->
    <el-row :gutter="20" class="stat-cards">
      <el-col :span="6">
        <el-card shadow="hover" class="charity-card">
          <div class="card-content">
            <div class="stat-icon">
              <el-icon><Sunny /></el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-number">{{ stats.totalCharities }}</div>
              <p class="stat-label">公益活动总数</p>
            </div>
          </div>
        </el-card>
      </el-col>

      <el-col :span="6">
        <el-card shadow="hover" class="question-card">
          <div class="card-content">
            <div class="stat-icon">
              <el-icon><ChatDotRound /></el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-number">{{ stats.totalQuestions }}</div>
              <p class="stat-label">题目总数</p>
            </div>
          </div>
        </el-card>
      </el-col>

      <el-col :span="6">
        <el-card shadow="hover" class="news-card">
          <div class="card-content">
            <div class="stat-icon">
              <el-icon><Notebook /></el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-number">{{ stats.totalNews }}</div>
              <p class="stat-label">新闻资讯</p>
            </div>
          </div>
        </el-card>
      </el-col>

      <el-col :span="6">
        <el-card shadow="hover" class="user-card">
          <div class="card-content">
            <div class="stat-icon">
              <el-icon><User /></el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-number">{{ stats.totalParticipants }}</div>
              <p class="stat-label">参与用户</p>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 活动状态统计 -->
    <el-row :gutter="20" class="activity-stats">
      <el-col :span="8">
        <el-card shadow="hover" class="ongoing-card">
          <div class="card-content">
            <div class="stat-number">{{ stats.ongoingActivities }}</div>
            <p class="stat-label">
              <el-tag type="success">进行中的活动</el-tag>
            </p>
          </div>
        </el-card>
      </el-col>

      <el-col :span="8">
        <el-card shadow="hover" class="upcoming-card">
          <div class="card-content">
            <div class="stat-number">{{ stats.upcomingActivities }}</div>
            <p class="stat-label">
              <el-tag type="warning">即将开始</el-tag>
            </p>
          </div>
        </el-card>
      </el-col>

      <el-col :span="8">
        <el-card shadow="hover" class="finished-card">
          <div class="card-content">
            <div class="stat-number">{{ stats.finishedActivities }}</div>
            <p class="stat-label">
              <el-tag type="info">已结束</el-tag>
            </p>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 最近活动 -->
    <el-card class="recent-activities" v-if="recentCharities.length > 0">
      <template #header>
        <div class="card-header">
          <h3>最近的公益活动</h3>
          <el-button type="text" @click="$router.push('/charity/list')">查看更多</el-button>
        </div>
      </template>
      <el-table :data="recentCharities" style="width: 100%">
        <el-table-column prop="title" label="活动标题" min-width="200" show-overflow-tooltip />
        <el-table-column prop="category" label="分类" width="100" />
        <el-table-column prop="status" label="状态" width="100">
          <template #default="scope">
            <el-tag :type="getStatusType(scope.row.status)">
              {{ getStatusText(scope.row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="participants" label="参与人数" width="120">
          <template #default="scope">
            {{ scope.row.participants }}/{{ scope.row.max_participants }}
          </template>
        </el-table-column>
        <el-table-column prop="location" label="地点" min-width="150" show-overflow-tooltip />
        <el-table-column prop="time" label="时间" min-width="150" />
      </el-table>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, reactive } from 'vue'
import { useUserStore } from '@/store/modules/user'
import { getDashboardData } from '@/api/dashboard'
import { getCharities } from '@/api/charity'
import { getQuestions } from '@/api/question'
import { getNewsList } from '@/api/news'
import { Refresh, Sunny, ChatDotRound, Notebook, User } from '@element-plus/icons-vue'

const userStore = useUserStore()
const currentTime = ref(new Date().toLocaleString())
const loading = ref(true)
const recentCharities = ref([])

// 统计数据
const stats = reactive({
  totalCharities: 0,
  totalQuestions: 0,
  totalNews: 0,
  totalParticipants: 0,
  ongoingActivities: 0,
  upcomingActivities: 0,
  finishedActivities: 0
})

// 工具方法
const getStatusType = (status: string) => {
  const statusMap = {
    upcoming: 'warning',
    ongoing: 'success',
    finished: 'info'
  }
  return statusMap[status] || 'info'
}

const getStatusText = (status: string) => {
  const statusMap = {
    upcoming: '即将开始',
    ongoing: '进行中',
    finished: '已结束'
  }
  return statusMap[status] || status
}

// 获取统计数据
const fetchStats = async () => {
  try {
    // 获取公益活动数据
    const charities = await getCharities({ limit: 1000 })
    stats.totalCharities = charities?.length || 0

    // 统计活动状态
    if (charities) {
      stats.ongoingActivities = charities.filter(item => item.status === 'ongoing').length
      stats.upcomingActivities = charities.filter(item => item.status === 'upcoming').length
      stats.finishedActivities = charities.filter(item => item.status === 'finished').length

      // 计算总参与人数
      stats.totalParticipants = charities.reduce((sum, item) => sum + (item.participants || 0), 0)

      // 获取最近的活动（前5个）
      recentCharities.value = charities.slice(0, 5)
    }

    // 获取题目数据
    const questions = await getQuestions({ limit: 1000 })
    stats.totalQuestions = questions?.length || 0

    // 获取新闻数据
    const news = await getNewsList({ limit: 1000 })
    stats.totalNews = news?.length || 0

  } catch (error) {
    console.error('获取统计数据失败:', error)
  }
}

// 刷新数据
const refreshData = async () => {
  loading.value = true
  try {
    await fetchStats()
    // 也可以调用原有的仪表盘数据接口
    const response = await getDashboardData()
    console.log('Dashboard data:', response)
  } catch (error) {
    console.error('刷新数据失败:', error)
  } finally {
    loading.value = false
  }
}

// 更新时间
let timer: number
onMounted(async () => {
  await refreshData()

  timer = window.setInterval(() => {
    currentTime.value = new Date().toLocaleString()
  }, 1000)
})

onUnmounted(() => {
  clearInterval(timer)
})
</script>

<style scoped>
.dashboard-container {
  padding: 20px;
}

.welcome-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.dashboard-content {
  line-height: 2;
  font-size: 16px;
}

.stat-cards {
  margin-top: 20px;
}

.activity-stats {
  margin-top: 20px;
}

.recent-activities {
  margin-top: 20px;
}

.card-content {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
  min-height: 100px;
}

.stat-icon {
  font-size: 48px;
  margin-right: 20px;
  opacity: 0.8;
}

.stat-info {
  text-align: center;
}

.stat-number {
  font-size: 36px;
  font-weight: bold;
  margin-bottom: 10px;
}

.stat-label {
  font-size: 14px;
  color: #606266;
  margin: 0;
}

/* 不同卡片的颜色主题 */
.charity-card .stat-icon {
  color: #f39c12;
}

.charity-card .stat-number {
  color: #f39c12;
}

.question-card .stat-icon {
  color: #3498db;
}

.question-card .stat-number {
  color: #3498db;
}

.news-card .stat-icon {
  color: #9b59b6;
}

.news-card .stat-number {
  color: #9b59b6;
}

.user-card .stat-icon {
  color: #2ecc71;
}

.user-card .stat-number {
  color: #2ecc71;
}

.ongoing-card .stat-number {
  color: #67c23a;
}

.upcoming-card .stat-number {
  color: #e6a23c;
}

.finished-card .stat-number {
  color: #909399;
}

/* 卡片悬停效果 */
.el-card:hover {
  transform: translateY(-2px);
  transition: all 0.3s ease;
}
</style>