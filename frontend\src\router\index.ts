import { createRouter, createWebHistory } from 'vue-router'
import type { RouteRecordRaw } from 'vue-router'
import Layout from '@/layout/index.vue'
import { useUserStore } from '@/store/modules/user'
import { getToken } from '@/utils/auth'

// 在routes数组中添加更多路由
const routes: Array<RouteRecordRaw> = [
  {
    path: '/login',
    name: 'Login',
    component: () => import('@/views/login/index.vue')
  },
  {
    path: '/',
    component: Layout,
    redirect: '/dashboard',
    meta: { title: '首页', icon: 'House' },
    children: [
      {
        path: 'dashboard',
        name: 'Dashboard',
        component: () => import('@/views/dashboard/index.vue'),
        meta: { title: '仪表盘', icon: 'DataLine' }
      }
    ]
  },
  // 添加更多菜单项
  {
    path: '/system',
    component: Layout,
    redirect: '/system/users',
    meta: { title: '系统管理', icon: 'Setting' },
    children: [
      {
        path: 'users',
        name: 'Users',
        component: () => import('@/views/system/users/index.vue'),
        meta: { title: '用户管理', icon: 'User' }
      },
      {
        path: 'roles',
        name: 'Roles',
        component: () => import('@/views/system/roles/index.vue'),
        meta: { title: '角色管理', icon: 'UserFilled' }
      }
    ]
  },
  // 公益活动管理
  {
    path: '/charity',
    component: Layout,
    redirect: '/charity/list',
    meta: { title: '公益活动', icon: 'Sunny' },
    children: [
      {
        path: 'list',
        name: 'CharityList',
        component: () => import('@/views/charity/list/index.vue'),
        meta: { title: '活动列表', icon: 'List' }
      },
      {
        path: 'create',
        name: 'CharityCreate',
        component: () => import('@/views/charity/form/index.vue'),
        meta: { title: '创建活动', icon: 'Plus' }
      },
      {
        path: 'categories',
        name: 'CharityCategories',
        component: () => import('@/views/charity/categories/index.vue'),
        meta: { title: '活动分类', icon: 'Collection' }
      }
    ]
  },
  // 问答系统管理
  {
    path: '/questions',
    component: Layout,
    redirect: '/questions/list',
    meta: { title: '问答系统', icon: 'ChatDotRound' },
    children: [
      {
        path: 'list',
        name: 'QuestionList',
        component: () => import('@/views/questions/list/index.vue'),
        meta: { title: '题目管理', icon: 'EditPen' }
      },
      {
        path: 'categories',
        name: 'QuestionCategories',
        component: () => import('@/views/questions/categories/index.vue'),
        meta: { title: '题目分类', icon: 'Collection' }
      },
      {
        path: 'history',
        name: 'QuizHistory',
        component: () => import('@/views/questions/history/index.vue'),
        meta: { title: '答题记录', icon: 'Clock' }
      }
    ]
  },
  // 新闻资讯管理
  {
    path: '/news',
    component: Layout,
    redirect: '/news/list',
    meta: { title: '新闻资讯', icon: 'Notebook' },
    children: [
      {
        path: 'list',
        name: 'NewsList',
        component: () => import('@/views/news/list/index.vue'),
        meta: { title: '新闻列表', icon: 'List' }
      },
      {
        path: 'create',
        name: 'NewsCreate',
        component: () => import('@/views/news/form/index.vue'),
        meta: { title: '发布新闻', icon: 'Plus' }
      }
    ]
  }
]

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes
})

const whiteList = ['/login'] // no redirect whitelist

router.beforeEach(async(to, from, next) => {
  const userStore = useUserStore()
  // 同时检查cookie和localStorage中的token
  const hasToken = getToken() || localStorage.getItem('token')

  if (hasToken) {
    if (to.path === '/login') {
      // if is logged in, redirect to the home page
      next({ path: '/' })
    } else {
        next()
    }
  } else {
    /* has no token*/
    if (whiteList.indexOf(to.path) !== -1) {
      // in the free login whitelist, go directly
      next()
    } else {
      // other pages that do not have permission to access are redirected to the login page.
      next(`/login?redirect=${to.path}`)
    }
  }
})

export default router