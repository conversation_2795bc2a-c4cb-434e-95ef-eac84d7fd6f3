#!/usr/bin/env python3
"""
初始化管理员账户脚本
运行此脚本来创建默认的管理员账户
"""

from sqlalchemy.orm import Session
from app.database import SessionLocal, engine, Base
from app.models import SysAdmin
from app.core.security import get_password_hash

def init_admin():
    try:
        # 测试数据库连接
        print("测试数据库连接...")
        engine.connect()
        print("数据库连接成功!")

        # 创建数据库表（如果不存在）
        print("创建数据库表...")
        Base.metadata.create_all(bind=engine)
        print("数据库表创建完成!")

        # 创建数据库会话
        db: Session = SessionLocal()

        # 检查是否已存在admin用户
        existing_admin = db.query(SysAdmin).filter(SysAdmin.username == "admin").first()
        
        if existing_admin:
            print("管理员账户已存在，更新密码...")
            existing_admin.password = get_password_hash("123456")
            db.commit()
            print("管理员密码已更新为: 123456")
        else:
            print("创建新的管理员账户...")
            # 创建管理员账户
            admin_user = SysAdmin(
                username="admin",
                password=get_password_hash("123456"),
                real_name="系统管理员",
                email="<EMAIL>",
                status=1,
                deleted=0
            )
            
            db.add(admin_user)
            db.commit()
            db.refresh(admin_user)
            
            print(f"管理员账户创建成功!")
            print(f"用户名: admin")
            print(f"密码: 123456")
            print(f"用户ID: {admin_user.id}")
            
        db.close()

    except Exception as e:
        print(f"初始化过程中出错: {e}")
        print("请检查:")
        print("1. MySQL服务是否正在运行")
        print("2. 数据库连接配置是否正确 (检查 .env 文件)")
        print("3. 数据库 'lun' 是否已创建")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    init_admin()
