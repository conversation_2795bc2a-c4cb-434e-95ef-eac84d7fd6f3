from typing import List, Optional
from sqlalchemy.orm import Session
from app.models.admin import SysAdmin
from app.schemas.admin import AdminCreate, AdminUpdate, AdminQuery
from app.core.security import get_password_hash

def get_admin_by_username(db: Session, *, username: str) -> Optional[SysAdmin]:
    return db.query(SysAdmin).filter(SysAdmin.username == username, SysAdmin.deleted == 0).first()

def get_admin(db: Session, admin_id: int) -> Optional[SysAdmin]:
    return db.query(SysAdmin).filter(SysAdmin.id == admin_id, SysAdmin.deleted == 0).first()

def get_admins(db: Session, skip: int = 0, limit: int = 100) -> List[SysAdmin]:
    return db.query(SysAdmin).filter(SysAdmin.deleted == 0).offset(skip).limit(limit).all()

def get_admins_by_query(db: Session, query: AdminQuery) -> List[SysAdmin]:
    db_query = db.query(SysAdmin).filter(SysAdmin.deleted == 0)

    if query.username:
        db_query = db_query.filter(SysAdmin.username.contains(query.username))
    if query.email:
        db_query = db_query.filter(SysAdmin.email.contains(query.email))
    if query.real_name:
        db_query = db_query.filter(SysAdmin.real_name.contains(query.real_name))
    if query.status is not None:
        db_query = db_query.filter(SysAdmin.status == query.status)

    return db_query.offset(query.skip).limit(query.limit).all()

def create_admin(db: Session, *, obj_in: AdminCreate) -> SysAdmin:
    db_obj = SysAdmin(
        username=obj_in.username,
        email=obj_in.email,
        real_name=obj_in.real_name,
        password=get_password_hash(obj_in.password), # Hash password before saving
        status=obj_in.status
    )
    db.add(db_obj)
    db.commit()
    db.refresh(db_obj)
    return db_obj

def update_admin(db: Session, admin_id: int, obj_in: AdminUpdate) -> Optional[SysAdmin]:
    db_admin = get_admin(db, admin_id)
    if db_admin:
        update_data = obj_in.dict(exclude_unset=True)

        # 如果更新密码，需要加密
        if 'password' in update_data and update_data['password']:
            update_data['password'] = get_password_hash(update_data['password'])

        for field, value in update_data.items():
            setattr(db_admin, field, value)

        db.commit()
        db.refresh(db_admin)
    return db_admin

def delete_admin(db: Session, admin_id: int) -> bool:
    db_admin = get_admin(db, admin_id)
    if db_admin:
        db_admin.deleted = 1
        db.commit()
        return True
    return False

def get_admin_count(db: Session) -> int:
    return db.query(SysAdmin).filter(SysAdmin.deleted == 0).count()