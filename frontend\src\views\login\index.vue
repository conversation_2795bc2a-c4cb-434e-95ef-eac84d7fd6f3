<template>
  <div class="login-container">
    <el-form
      ref="loginFormRef"
      :model="loginForm"
      :rules="loginRules"
      class="login-form"
      auto-complete="on"
      label-position="left"
    >
      <div class="title-container">
        <h3 class="title">登录</h3>
      </div>

      <el-form-item prop="username">
        <el-input
          ref="username"
          v-model="loginForm.username"
          placeholder="Username"
          name="username"
          type="text"
          tabindex="1"
          auto-complete="on"
        />
      </el-form-item>

      <el-form-item prop="password">
        <el-input
          v-model="loginForm.password"
          type="password"
          placeholder="Password"
          name="password"
          tabindex="2"
          auto-complete="on"
          @keyup.enter="handleLogin"
        />
      </el-form-item>

      <el-button
        :loading="loading.value"
        type="primary"
        style="width:100%;margin-bottom:30px;"
        @click.prevent="handleLogin"
      >
        登录
      </el-button>
    </el-form>
  </div>
</template>

<script setup lang="ts">
import { reactive, ref } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { useUserStore } from '@/store/modules/user'
import { ElMessage } from 'element-plus'
import type { FormInstance } from 'element-plus'

const router = useRouter()
const route = useRoute()
const userStore = useUserStore()
const loginFormRef = ref<FormInstance>()

const loginForm = reactive({
  username: 'admin', // Default for testing
  password: '123456' // Default for testing - matches database
})

const loginRules = {
  username: [{ required: true, message: '请输入用户名', trigger: 'blur' }],
  password: [{ required: true, message: '请输入密码', trigger: 'blur' }]
}

const loading = reactive({ value: false })

const handleLogin = () => {
  loginFormRef.value?.validate((valid) => {
    if (valid) {
      loading.value = true
      userStore.login(loginForm)
        .then(() => {
          ElMessage.success('登录成功')
          const redirect = route.query.redirect as string
          router.push({ path: redirect || '/' })
          loading.value = false
        })
        .catch((error) => {
          console.error('登录失败:', error)
          ElMessage.error('登录失败: ' + (error.message || '用户名或密码错误'))
          loading.value = false
        })
    } else {
      return false
    }
  })
}
</script>

<style scoped>
.login-container {
  min-height: 100%;
  width: 100%;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
}
.login-form {
  position: relative;
  width: 520px;
  max-width: 100%;
  padding: 35px;
  margin: 0 auto;
  overflow: hidden;
  border: 1px solid #ebeef5;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0,0,0,.1);
}
.title-container {
  position: relative;
}
.title {
  font-size: 26px;
  color: #333;
  margin: 0px auto 40px auto;
  text-align: center;
  font-weight: bold;
}
</style>