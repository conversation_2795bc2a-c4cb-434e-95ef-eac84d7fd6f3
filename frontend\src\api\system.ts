import request from '@/utils/request'

// 用户管理相关接口
export function getUsers(params?: any) {
  return request({
    url: '/users/',
    method: 'get',
    params
  })
}

export function getUser(id: number) {
  return request({
    url: `/users/${id}`,
    method: 'get'
  })
}

export function createUser(data: any) {
  return request({
    url: '/users/',
    method: 'post',
    data
  })
}

export function updateUser(id: number, data: any) {
  return request({
    url: `/users/${id}`,
    method: 'put',
    data
  })
}

export function deleteUser(id: number) {
  return request({
    url: `/users/${id}`,
    method: 'delete'
  })
}

export function assignUserRoles(userId: number, roleIds: number[]) {
  return request({
    url: `/users/${userId}/roles`,
    method: 'post',
    data: {
      user_id: userId,
      role_ids: roleIds
    }
  })
}

export function getUserRoles(userId: number) {
  return request({
    url: `/users/${userId}/roles`,
    method: 'get'
  })
}

export function getUserStats() {
  return request({
    url: '/users/stats/count',
    method: 'get'
  })
}

// 角色管理相关接口
export function getRoles(params?: any) {
  return request({
    url: '/roles/',
    method: 'get',
    params
  })
}

export function getRole(id: number) {
  return request({
    url: `/roles/${id}`,
    method: 'get'
  })
}

export function createRole(data: any) {
  return request({
    url: '/roles/',
    method: 'post',
    data
  })
}

export function updateRole(id: number, data: any) {
  return request({
    url: `/roles/${id}`,
    method: 'put',
    data
  })
}

export function deleteRole(id: number) {
  return request({
    url: `/roles/${id}`,
    method: 'delete'
  })
}

export function assignRolePermissions(roleId: number, permissionIds: number[]) {
  return request({
    url: `/roles/${roleId}/permissions`,
    method: 'post',
    data: {
      role_id: roleId,
      permission_ids: permissionIds
    }
  })
}

export function getRolePermissions(roleId: number) {
  return request({
    url: `/roles/${roleId}/permissions`,
    method: 'get'
  })
}

// 权限管理相关接口
export function getPermissions(params?: any) {
  return request({
    url: '/roles/permissions/',
    method: 'get',
    params
  })
}

export function getPermission(id: number) {
  return request({
    url: `/roles/permissions/${id}`,
    method: 'get'
  })
}

export function createPermission(data: any) {
  return request({
    url: '/roles/permissions/',
    method: 'post',
    data
  })
}

export function updatePermission(id: number, data: any) {
  return request({
    url: `/roles/permissions/${id}`,
    method: 'put',
    data
  })
}

export function deletePermission(id: number) {
  return request({
    url: `/roles/permissions/${id}`,
    method: 'delete'
  })
}
