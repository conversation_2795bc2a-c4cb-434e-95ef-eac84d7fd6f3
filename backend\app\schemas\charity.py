from pydantic import BaseModel
from typing import Optional
from datetime import datetime

# 公益活动基础模式
class CharityBase(BaseModel):
    title: str
    description: Optional[str] = None
    content: Optional[str] = None
    time: Optional[str] = None
    location: Optional[str] = None
    max_participants: Optional[int] = 0
    image: Optional[str] = None
    status: Optional[str] = "upcoming"
    category: Optional[str] = "环保"
    organizer: Optional[str] = None
    contact: Optional[str] = None
    requirements: Optional[str] = None
    notices: Optional[str] = None

# 创建公益活动
class CharityCreate(CharityBase):
    pass

# 更新公益活动
class CharityUpdate(CharityBase):
    title: Optional[str] = None

# 数据库中的公益活动
class CharityInDBBase(CharityBase):
    id: int
    participants: int
    create_time: Optional[datetime] = None
    update_time: Optional[datetime] = None

    class Config:
        from_attributes = True

# 返回给客户端的公益活动
class Charity(CharityInDBBase):
    pass

# 公益活动分类基础模式
class CharityCategoryBase(BaseModel):
    name: str

# 创建公益活动分类
class CharityCategoryCreate(CharityCategoryBase):
    pass

# 更新公益活动分类
class CharityCategoryUpdate(CharityCategoryBase):
    name: Optional[str] = None

# 数据库中的公益活动分类
class CharityCategoryInDBBase(CharityCategoryBase):
    id: int
    create_time: Optional[datetime] = None
    update_time: Optional[datetime] = None

    class Config:
        from_attributes = True

# 返回给客户端的公益活动分类
class CharityCategory(CharityCategoryInDBBase):
    pass
