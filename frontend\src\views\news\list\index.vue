<template>
  <div class="news-list">
    <el-card class="box-card">
      <template #header>
        <div class="card-header">
          <span>新闻资讯管理</span>
          <el-button type="primary" @click="handleCreate">
            <el-icon><Plus /></el-icon>
            发布新闻
          </el-button>
        </div>
      </template>

      <!-- 搜索栏 -->
      <div class="search-bar">
        <el-form :inline="true" :model="searchForm" class="search-form">
          <el-form-item label="新闻标题">
            <el-input v-model="searchForm.title" placeholder="请输入新闻标题" clearable />
          </el-form-item>
          <el-form-item label="新闻分类">
            <el-select v-model="searchForm.category" placeholder="请选择分类" clearable>
              <el-option label="环保资讯" value="1" />
              <el-option label="科技新闻" value="2" />
              <el-option label="法律法规" value="3" />
              <el-option label="安全知识" value="4" />
              <el-option label="文化教育" value="5" />
            </el-select>
          </el-form-item>
          <el-form-item label="作者">
            <el-input v-model="searchForm.author" placeholder="请输入作者" clearable />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="handleSearch">搜索</el-button>
            <el-button @click="handleReset">重置</el-button>
          </el-form-item>
        </el-form>
      </div>

      <!-- 数据表格 -->
      <el-table :data="tableData" style="width: 100%" v-loading="loading">
        <el-table-column prop="id" label="ID" width="80" />
        <el-table-column prop="title" label="新闻标题" min-width="250" show-overflow-tooltip />
        <el-table-column prop="category" label="分类" width="100">
          <template #default="scope">
            {{ getCategoryText(scope.row.category) }}
          </template>
        </el-table-column>
        <el-table-column prop="author" label="作者" width="120" />
        <el-table-column prop="read_count" label="阅读量" width="100">
          <template #default="scope">
            <el-tag type="info">{{ scope.row.read_count }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="comments" label="评论数" width="100">
          <template #default="scope">
            <el-tag type="warning">{{ scope.row.comments }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="publish_date" label="发布日期" width="120" />
        <el-table-column prop="create_time" label="创建时间" width="180">
          <template #default="scope">
            {{ formatDate(scope.row.create_time) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="scope">
            <el-button size="small" @click="handleView(scope.row)">查看</el-button>
            <el-button size="small" type="primary" @click="handleEdit(scope.row)">编辑</el-button>
            <el-button size="small" type="danger" @click="handleDelete(scope.row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination">
        <el-pagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.size"
          :page-sizes="[10, 20, 50, 100]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 查看详情对话框 -->
    <el-dialog v-model="dialogVisible" title="新闻详情" width="70%">
      <div v-if="currentItem">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="新闻标题" :span="2">{{ currentItem.title }}</el-descriptions-item>
          <el-descriptions-item label="分类">{{ getCategoryText(currentItem.category) }}</el-descriptions-item>
          <el-descriptions-item label="作者">{{ currentItem.author }}</el-descriptions-item>
          <el-descriptions-item label="发布日期">{{ currentItem.publish_date }}</el-descriptions-item>
          <el-descriptions-item label="阅读量">{{ currentItem.read_count }}</el-descriptions-item>
          <el-descriptions-item label="评论数">{{ currentItem.comments }}</el-descriptions-item>
          <el-descriptions-item label="创建时间">{{ formatDate(currentItem.create_time) }}</el-descriptions-item>
          <el-descriptions-item label="新闻描述" :span="2">{{ currentItem.description }}</el-descriptions-item>
        </el-descriptions>
        
        <div class="news-content" style="margin-top: 20px;">
          <h4>新闻内容</h4>
          <div class="content-box" v-html="currentItem.content"></div>
        </div>
        
        <div v-if="currentItem.image" class="news-image" style="margin-top: 20px;">
          <h4>新闻图片</h4>
          <el-image
            :src="currentItem.image"
            style="width: 200px; height: 150px"
            fit="cover"
            :preview-src-list="[currentItem.image]"
          />
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus } from '@element-plus/icons-vue'
import { getNewsList, deleteNews } from '@/api/news'
import { useRouter } from 'vue-router'

const router = useRouter()

// 响应式数据
const loading = ref(false)
const tableData = ref([])
const dialogVisible = ref(false)
const currentItem = ref(null)

// 搜索表单
const searchForm = reactive({
  title: '',
  category: '',
  author: ''
})

// 分页
const pagination = reactive({
  page: 1,
  size: 10,
  total: 0
})

// 工具方法
const formatDate = (dateString: string) => {
  if (!dateString) return '-'
  return new Date(dateString).toLocaleString()
}

const getCategoryText = (category: string) => {
  const categoryMap = {
    '1': '环保资讯',
    '2': '科技新闻',
    '3': '法律法规',
    '4': '安全知识',
    '5': '文化教育'
  }
  return categoryMap[category] || category
}

// 获取数据
const fetchData = async () => {
  loading.value = true
  try {
    const params = {
      skip: (pagination.page - 1) * pagination.size,
      limit: pagination.size,
      ...searchForm
    }
    const response = await getNewsList(params)
    tableData.value = response || []
    pagination.total = tableData.value.length
  } catch (error) {
    ElMessage.error('获取数据失败')
    console.error(error)
  } finally {
    loading.value = false
  }
}

// 事件处理
const handleSearch = () => {
  pagination.page = 1
  fetchData()
}

const handleReset = () => {
  Object.keys(searchForm).forEach(key => {
    searchForm[key] = ''
  })
  pagination.page = 1
  fetchData()
}

const handleCreate = () => {
  router.push('/news/create')
}

const handleView = (row: any) => {
  currentItem.value = row
  dialogVisible.value = true
}

const handleEdit = (row: any) => {
  router.push(`/news/create?id=${row.id}`)
}

const handleDelete = async (row: any) => {
  try {
    await ElMessageBox.confirm('确定要删除这条新闻吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    
    await deleteNews(row.id)
    ElMessage.success('删除成功')
    fetchData()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败')
    }
  }
}

const handleSizeChange = (size: number) => {
  pagination.size = size
  pagination.page = 1
  fetchData()
}

const handleCurrentChange = (page: number) => {
  pagination.page = page
  fetchData()
}

// 初始化
onMounted(() => {
  fetchData()
})
</script>

<style scoped>
.news-list {
  padding: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.search-bar {
  margin-bottom: 20px;
}

.search-form {
  background-color: #f5f5f5;
  padding: 20px;
  border-radius: 4px;
}

.pagination {
  margin-top: 20px;
  text-align: right;
}

.content-box {
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  padding: 15px;
  background-color: #fafafa;
  max-height: 300px;
  overflow-y: auto;
}

.news-content h4,
.news-image h4 {
  margin-bottom: 10px;
  color: #303133;
}
</style>
