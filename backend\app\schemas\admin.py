from pydantic import BaseModel
from typing import Optional, List
from datetime import datetime

# Shared properties
class AdminBase(BaseModel):
    username: str
    email: Optional[str] = None
    real_name: Optional[str] = None
    status: Optional[int] = 1

# Properties to receive on item creation
class AdminCreate(AdminBase):
    password: str

# Properties to receive on item update
class AdminUpdate(BaseModel):
    username: Optional[str] = None
    email: Optional[str] = None
    real_name: Optional[str] = None
    status: Optional[int] = None
    password: Optional[str] = None

# Properties shared by models stored in DB
class AdminInDBBase(AdminBase):
    id: int
    create_time: Optional[datetime] = None
    update_time: Optional[datetime] = None

    class Config:
        from_attributes = True

# Properties to return to client
class Admin(AdminInDBBase):
    roles: Optional[List[str]] = []  # 用户角色列表

# Properties stored in DB
class AdminInDB(AdminInDBBase):
    password: str  # 存储加密后的密码

# 用户列表查询参数
class AdminQuery(BaseModel):
    username: Optional[str] = None
    email: Optional[str] = None
    real_name: Optional[str] = None
    status: Optional[int] = None
    skip: Optional[int] = 0
    limit: Optional[int] = 100