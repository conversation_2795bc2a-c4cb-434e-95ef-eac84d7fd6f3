<template>
  <div class="user-management">
    <el-card class="box-card">
      <template #header>
        <div class="card-header">
          <span>用户管理</span>
          <el-button type="primary" @click="handleCreate">
            <el-icon><Plus /></el-icon>
            新建用户
          </el-button>
        </div>
      </template>

      <!-- 搜索栏 -->
      <div class="search-bar">
        <el-form :inline="true" :model="searchForm" class="search-form">
          <el-form-item label="用户名">
            <el-input v-model="searchForm.username" placeholder="请输入用户名" clearable />
          </el-form-item>
          <el-form-item label="真实姓名">
            <el-input v-model="searchForm.real_name" placeholder="请输入真实姓名" clearable />
          </el-form-item>
          <el-form-item label="邮箱">
            <el-input v-model="searchForm.email" placeholder="请输入邮箱" clearable />
          </el-form-item>
          <el-form-item label="状态">
            <el-select v-model="searchForm.status" placeholder="请选择状态" clearable>
              <el-option label="启用" :value="1" />
              <el-option label="禁用" :value="0" />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="handleSearch">搜索</el-button>
            <el-button @click="handleReset">重置</el-button>
          </el-form-item>
        </el-form>
      </div>

      <!-- 数据表格 -->
      <el-table :data="tableData" style="width: 100%" v-loading="loading">
        <el-table-column prop="id" label="ID" width="80" />
        <el-table-column prop="username" label="用户名" min-width="120" />
        <el-table-column prop="real_name" label="真实姓名" min-width="120" />
        <el-table-column prop="email" label="邮箱" min-width="180" />
        <el-table-column prop="roles" label="角色" min-width="150">
          <template #default="scope">
            <el-tag
              v-for="role in scope.row.roles"
              :key="role"
              type="success"
              size="small"
              style="margin-right: 5px;"
            >
              {{ role }}
            </el-tag>
            <span v-if="!scope.row.roles || scope.row.roles.length === 0" class="text-muted">
              未分配角色
            </span>
          </template>
        </el-table-column>
        <el-table-column prop="status" label="状态" width="100">
          <template #default="scope">
            <el-tag :type="scope.row.status === 1 ? 'success' : 'danger'">
              {{ scope.row.status === 1 ? '启用' : '禁用' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="create_time" label="创建时间" width="180">
          <template #default="scope">
            {{ formatDate(scope.row.create_time) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="250" fixed="right">
          <template #default="scope">
            <el-button size="small" @click="handleView(scope.row)">查看</el-button>
            <el-button size="small" type="primary" @click="handleEdit(scope.row)">编辑</el-button>
            <el-button size="small" type="warning" @click="handleAssignRoles(scope.row)">分配角色</el-button>
            <el-button size="small" type="danger" @click="handleDelete(scope.row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination">
        <el-pagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.size"
          :page-sizes="[10, 20, 50, 100]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 查看详情对话框 -->
    <el-dialog v-model="dialogVisible" title="用户详情" width="60%">
      <div v-if="currentItem">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="用户ID">{{ currentItem.id }}</el-descriptions-item>
          <el-descriptions-item label="用户名">{{ currentItem.username }}</el-descriptions-item>
          <el-descriptions-item label="真实姓名">{{ currentItem.real_name }}</el-descriptions-item>
          <el-descriptions-item label="邮箱">{{ currentItem.email }}</el-descriptions-item>
          <el-descriptions-item label="状态">
            <el-tag :type="currentItem.status === 1 ? 'success' : 'danger'">
              {{ currentItem.status === 1 ? '启用' : '禁用' }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="创建时间">{{ formatDate(currentItem.create_time) }}</el-descriptions-item>
          <el-descriptions-item label="更新时间">{{ formatDate(currentItem.update_time) }}</el-descriptions-item>
          <el-descriptions-item label="用户角色" :span="2">
            <el-tag
              v-for="role in currentItem.roles"
              :key="role"
              type="success"
              size="small"
              style="margin-right: 5px;"
            >
              {{ role }}
            </el-tag>
            <span v-if="!currentItem.roles || currentItem.roles.length === 0" class="text-muted">
              未分配角色
            </span>
          </el-descriptions-item>
        </el-descriptions>
      </div>
    </el-dialog>

    <!-- 新建/编辑用户对话框 -->
    <el-dialog
      v-model="formDialogVisible"
      :title="isEdit ? '编辑用户' : '新建用户'"
      width="50%"
      :close-on-click-modal="false"
    >
      <el-form
        ref="formRef"
        :model="form"
        :rules="rules"
        label-width="100px"
      >
        <el-form-item label="用户名" prop="username">
          <el-input v-model="form.username" placeholder="请输入用户名" />
        </el-form-item>
        <el-form-item label="真实姓名" prop="real_name">
          <el-input v-model="form.real_name" placeholder="请输入真实姓名" />
        </el-form-item>
        <el-form-item label="邮箱" prop="email">
          <el-input v-model="form.email" placeholder="请输入邮箱" />
        </el-form-item>
        <el-form-item label="密码" prop="password">
          <el-input
            v-model="form.password"
            type="password"
            :placeholder="isEdit ? '留空则不修改密码' : '请输入密码'"
            show-password
          />
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-radio-group v-model="form.status">
            <el-radio :label="1">启用</el-radio>
            <el-radio :label="0">禁用</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="formDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleSubmit" :loading="submitLoading">
            {{ isEdit ? '更新' : '创建' }}
          </el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 分配角色对话框 -->
    <el-dialog
      v-model="roleDialogVisible"
      title="分配角色"
      width="50%"
    >
      <div v-if="currentUser">
        <p>为用户 <strong>{{ currentUser.username }}</strong> 分配角色：</p>
        <el-checkbox-group v-model="selectedRoles" style="margin-top: 20px;">
          <el-checkbox
            v-for="role in allRoles"
            :key="role.id"
            :label="role.id"
            style="display: block; margin-bottom: 10px;"
          >
            {{ role.name }} ({{ role.code }})
            <span v-if="role.description" class="role-description">- {{ role.description }}</span>
          </el-checkbox>
        </el-checkbox-group>
      </div>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="roleDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleRoleSubmit" :loading="roleSubmitLoading">
            确定
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus } from '@element-plus/icons-vue'
import type { FormInstance } from 'element-plus'
import { getUsers, createUser, updateUser, deleteUser, assignUserRoles, getUserRoles, getRoles } from '@/api/system'

// 响应式数据
const loading = ref(false)
const submitLoading = ref(false)
const roleSubmitLoading = ref(false)
const tableData = ref([])
const dialogVisible = ref(false)
const formDialogVisible = ref(false)
const roleDialogVisible = ref(false)
const isEdit = ref(false)
const currentItem = ref(null)
const currentUser = ref(null)
const currentId = ref<number | null>(null)
const formRef = ref<FormInstance>()
const allRoles = ref([])
const selectedRoles = ref([])

// 搜索表单
const searchForm = reactive({
  username: '',
  real_name: '',
  email: '',
  status: null
})

// 用户表单
const form = reactive({
  username: '',
  real_name: '',
  email: '',
  password: '',
  status: 1
})

// 分页
const pagination = reactive({
  page: 1,
  size: 10,
  total: 0
})

// 表单验证规则
const rules = {
  username: [
    { required: true, message: '请输入用户名', trigger: 'blur' },
    { min: 3, max: 20, message: '用户名长度在 3 到 20 个字符', trigger: 'blur' }
  ],
  real_name: [
    { required: true, message: '请输入真实姓名', trigger: 'blur' }
  ],
  email: [
    { required: true, message: '请输入邮箱', trigger: 'blur' },
    { type: 'email', message: '请输入正确的邮箱格式', trigger: 'blur' }
  ],
  password: [
    {
      validator: (rule: any, value: string, callback: Function) => {
        if (!isEdit.value && !value) {
          callback(new Error('请输入密码'))
        } else if (value && value.length < 6) {
          callback(new Error('密码长度不能少于6位'))
        } else {
          callback()
        }
      },
      trigger: 'blur'
    }
  ]
}

// 工具方法
const formatDate = (dateString: string) => {
  if (!dateString) return '-'
  return new Date(dateString).toLocaleString()
}

// 获取数据
const fetchData = async () => {
  loading.value = true
  try {
    const params = {
      skip: (pagination.page - 1) * pagination.size,
      limit: pagination.size,
      ...searchForm
    }
    const response = await getUsers(params)
    tableData.value = response || []
    pagination.total = tableData.value.length
  } catch (error) {
    ElMessage.error('获取数据失败')
    console.error(error)
  } finally {
    loading.value = false
  }
}

// 获取所有角色
const fetchRoles = async () => {
  try {
    const response = await getRoles({ limit: 1000 })
    allRoles.value = response || []
  } catch (error) {
    console.error('获取角色列表失败:', error)
  }
}

// 事件处理
const handleSearch = () => {
  pagination.page = 1
  fetchData()
}

const handleReset = () => {
  Object.keys(searchForm).forEach(key => {
    searchForm[key] = key === 'status' ? null : ''
  })
  pagination.page = 1
  fetchData()
}

const handleCreate = () => {
  isEdit.value = false
  currentId.value = null
  resetForm()
  formDialogVisible.value = true
}

const handleView = (row: any) => {
  currentItem.value = row
  dialogVisible.value = true
}

const handleEdit = (row: any) => {
  isEdit.value = true
  currentId.value = row.id

  // 填充表单数据
  form.username = row.username
  form.real_name = row.real_name
  form.email = row.email
  form.password = ''
  form.status = row.status

  formDialogVisible.value = true
}

const handleDelete = async (row: any) => {
  try {
    await ElMessageBox.confirm('确定要删除这个用户吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })

    await deleteUser(row.id)
    ElMessage.success('删除成功')
    fetchData()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败')
    }
  }
}

const handleAssignRoles = async (row: any) => {
  currentUser.value = row

  // 获取用户当前角色
  try {
    const userRoles = await getUserRoles(row.id)
    selectedRoles.value = userRoles.map((role: any) => role.id)
  } catch (error) {
    selectedRoles.value = []
  }

  roleDialogVisible.value = true
}

const resetForm = () => {
  form.username = ''
  form.real_name = ''
  form.email = ''
  form.password = ''
  form.status = 1
}

const handleSubmit = async () => {
  if (!formRef.value) return

  try {
    await formRef.value.validate()
    submitLoading.value = true

    if (isEdit.value && currentId.value) {
      await updateUser(currentId.value, form)
      ElMessage.success('更新成功')
    } else {
      await createUser(form)
      ElMessage.success('创建成功')
    }

    formDialogVisible.value = false
    fetchData()
  } catch (error) {
    if (error !== false) {
      ElMessage.error(isEdit.value ? '更新失败' : '创建失败')
      console.error(error)
    }
  } finally {
    submitLoading.value = false
  }
}

const handleRoleSubmit = async () => {
  if (!currentUser.value) return

  try {
    roleSubmitLoading.value = true
    await assignUserRoles(currentUser.value.id, selectedRoles.value)
    ElMessage.success('角色分配成功')
    roleDialogVisible.value = false
    fetchData()
  } catch (error) {
    ElMessage.error('角色分配失败')
    console.error(error)
  } finally {
    roleSubmitLoading.value = false
  }
}

const handleSizeChange = (size: number) => {
  pagination.size = size
  pagination.page = 1
  fetchData()
}

const handleCurrentChange = (page: number) => {
  pagination.page = page
  fetchData()
}

// 初始化
onMounted(() => {
  fetchData()
  fetchRoles()
})
</script>

<style scoped>
.user-management {
  padding: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.search-bar {
  margin-bottom: 20px;
}

.search-form {
  background-color: #f5f5f5;
  padding: 20px;
  border-radius: 4px;
}

.pagination {
  margin-top: 20px;
  text-align: right;
}

.text-muted {
  color: #909399;
  font-style: italic;
}

.role-description {
  color: #909399;
  font-size: 12px;
}
</style>