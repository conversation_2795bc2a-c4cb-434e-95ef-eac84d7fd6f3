<template>
  <div class="app-wrapper">
    <!-- 侧边栏 -->
    <div class="sidebar-container">
      <el-menu
        :default-active="activeMenu"
        class="sidebar-menu"
        background-color="#304156"
        text-color="#bfcbd9"
        active-text-color="#409EFF"
        router
      >
        <template v-for="route in routes" :key="route.path">
          <!-- 带子菜单的路由 -->
          <el-sub-menu v-if="route.children && route.children.length > 0" :index="route.path">
            <template #title>
              <el-icon v-if="route.meta && route.meta.icon">
                <component :is="route.meta.icon" />
              </el-icon>
              <span>{{ route.meta ? route.meta.title : route.name }}</span>
            </template>
            <el-menu-item
              v-for="child in route.children"
              :key="route.path + '/' + child.path"
              :index="route.path === '/' ? '/' + child.path : route.path + '/' + child.path"
            >
              <el-icon v-if="child.meta && child.meta.icon">
                <component :is="child.meta.icon" />
              </el-icon>
              <span>{{ child.meta ? child.meta.title : child.name }}</span>
            </el-menu-item>
          </el-sub-menu>
          <!-- 不带子菜单的路由 -->
          <el-menu-item v-else :index="route.path">
            <el-icon v-if="route.meta && route.meta.icon">
              <component :is="route.meta.icon" />
            </el-icon>
            <span>{{ route.meta ? route.meta.title : route.name }}</span>
          </el-menu-item>
        </template>
      </el-menu>
    </div>
    
    <div class="main-container">
      <!-- 导航栏 -->
      <el-header class="navbar">
        <div class="navbar-left">
          <h2>管理系统</h2>
        </div>
        <div class="navbar-right">
          <el-dropdown @command="handleCommand">
            <span class="el-dropdown-link">
              {{ userStore.username || '用户' }}<el-icon class="el-icon--right"><arrow-down /></el-icon>
            </span>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item command="logout">退出登录</el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </div>
      </el-header>
      
      <section class="app-main">
        <router-view />
      </section>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useUserStore } from '@/store/modules/user'
import { ArrowDown } from '@element-plus/icons-vue'

const route = useRoute()
const router = useRouter()
const userStore = useUserStore()

const routes = computed(() => {
  // 返回所有可用于侧边栏的路由
  return router.options.routes.filter(route => !route.path.startsWith('/login'))
})

const activeMenu = computed(() => {
  return route.path
})

const handleCommand = (command: string) => {
  if (command === 'logout') {
    userStore.logout()
  }
}
</script>

<style scoped>
.app-wrapper {
  position: relative;
  height: 100%;
  width: 100%;
  display: flex;
}

.sidebar-container {
  width: 210px;
  height: 100vh;
  position: fixed;
  left: 0;
  top: 0;
  bottom: 0;
  background-color: #304156;
  z-index: 1001;
  overflow: hidden;
}

.sidebar-menu {
  border-right: none;
  height: 100%;
}

.main-container {
  margin-left: 210px;
  width: calc(100% - 210px);
  min-height: 100vh;
}

.navbar {
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  background-color: #fff;
  box-shadow: 0 1px 4px rgba(0, 21, 41, 0.08);
  padding: 0 20px;
}

.navbar-left {
  display: flex;
  align-items: center;
}

.navbar-right {
  display: flex;
  align-items: center;
}

.el-dropdown-link {
  cursor: pointer;
  display: flex;
  align-items: center;
  font-size: 14px;
  color: #606266;
}

.app-main {
  min-height: calc(100vh - 60px);
  width: 100%;
  position: relative;
  overflow: hidden;
  padding: 20px;
}
</style>