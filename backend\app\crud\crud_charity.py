from typing import List, Optional
from sqlalchemy.orm import Session
from app.models.charity import Charity, CharityCategory
from app.schemas.charity import CharityCreate, CharityUpdate, CharityCategoryCreate, CharityCategoryUpdate

# 公益活动CRUD操作
def get_charity(db: Session, charity_id: int) -> Optional[Charity]:
    return db.query(Charity).filter(Charity.id == charity_id, Charity.deleted == 0).first()

def get_charities(db: Session, skip: int = 0, limit: int = 100) -> List[Charity]:
    return db.query(Charity).filter(Charity.deleted == 0).offset(skip).limit(limit).all()

def get_charities_by_category(db: Session, category: str, skip: int = 0, limit: int = 100) -> List[Charity]:
    return db.query(Charity).filter(Charity.category == category, Charity.deleted == 0).offset(skip).limit(limit).all()

def get_charities_by_status(db: Session, status: str, skip: int = 0, limit: int = 100) -> List[Charity]:
    return db.query(Charity).filter(Charity.status == status, Charity.deleted == 0).offset(skip).limit(limit).all()

def create_charity(db: Session, charity: CharityCreate) -> Charity:
    db_charity = Charity(**charity.dict())
    db.add(db_charity)
    db.commit()
    db.refresh(db_charity)
    return db_charity

def update_charity(db: Session, charity_id: int, charity: CharityUpdate) -> Optional[Charity]:
    db_charity = get_charity(db, charity_id)
    if db_charity:
        update_data = charity.dict(exclude_unset=True)
        for field, value in update_data.items():
            setattr(db_charity, field, value)
        db.commit()
        db.refresh(db_charity)
    return db_charity

def delete_charity(db: Session, charity_id: int) -> bool:
    db_charity = get_charity(db, charity_id)
    if db_charity:
        db_charity.deleted = 1
        db.commit()
        return True
    return False

# 公益活动分类CRUD操作
def get_charity_category(db: Session, category_id: int) -> Optional[CharityCategory]:
    return db.query(CharityCategory).filter(CharityCategory.id == category_id, CharityCategory.deleted == 0).first()

def get_charity_categories(db: Session, skip: int = 0, limit: int = 100) -> List[CharityCategory]:
    return db.query(CharityCategory).filter(CharityCategory.deleted == 0).offset(skip).limit(limit).all()

def create_charity_category(db: Session, category: CharityCategoryCreate) -> CharityCategory:
    db_category = CharityCategory(**category.dict())
    db.add(db_category)
    db.commit()
    db.refresh(db_category)
    return db_category

def update_charity_category(db: Session, category_id: int, category: CharityCategoryUpdate) -> Optional[CharityCategory]:
    db_category = get_charity_category(db, category_id)
    if db_category:
        update_data = category.dict(exclude_unset=True)
        for field, value in update_data.items():
            setattr(db_category, field, value)
        db.commit()
        db.refresh(db_category)
    return db_category

def delete_charity_category(db: Session, category_id: int) -> bool:
    db_category = get_charity_category(db, category_id)
    if db_category:
        db_category.deleted = 1
        db.commit()
        return True
    return False
