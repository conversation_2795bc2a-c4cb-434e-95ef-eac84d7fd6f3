import request from '@/utils/request'

// 题目相关接口
export function getQuestions(params?: any) {
  return request({
    url: '/questions/',
    method: 'get',
    params
  })
}

export function getQuestion(id: number) {
  return request({
    url: `/questions/${id}`,
    method: 'get'
  })
}

export function createQuestion(data: any) {
  return request({
    url: '/questions/',
    method: 'post',
    data
  })
}

export function updateQuestion(id: number, data: any) {
  return request({
    url: `/questions/${id}`,
    method: 'put',
    data
  })
}

export function deleteQuestion(id: number) {
  return request({
    url: `/questions/${id}`,
    method: 'delete'
  })
}

// 题目分类相关接口
export function getCategories(params?: any) {
  return request({
    url: '/questions/categories/',
    method: 'get',
    params
  })
}

export function getCategory(id: number) {
  return request({
    url: `/questions/categories/${id}`,
    method: 'get'
  })
}

export function createCategory(data: any) {
  return request({
    url: '/questions/categories/',
    method: 'post',
    data
  })
}

export function updateCategory(id: number, data: any) {
  return request({
    url: `/questions/categories/${id}`,
    method: 'put',
    data
  })
}

export function deleteCategory(id: number) {
  return request({
    url: `/questions/categories/${id}`,
    method: 'delete'
  })
}

// 答题历史相关接口
export function getQuizHistories(params?: any) {
  return request({
    url: '/questions/quiz-history/',
    method: 'get',
    params
  })
}

export function createQuizHistory(data: any) {
  return request({
    url: '/questions/quiz-history/',
    method: 'post',
    data
  })
}
