from .admin import Admin, Admin<PERSON><PERSON>, AdminUpdate, AdminQuery
from .token import Token, TokenData
from .charity import Charity, CharityCreate, CharityUpdate, CharityCategory, CharityCategoryCreate, CharityCategoryUpdate
from .question import Question, QuestionCreate, QuestionUpdate, Category, CategoryCreate, CategoryUpdate, QuizHistory, QuizHistoryCreate
from .news import NewsInfo, NewsInfoCreate, NewsInfoUpdate
from .role import Role, RoleCreate, RoleUpdate, Permission, PermissionCreate, PermissionUpdate, RolePermission, UserRole, RolePermissionAssign, UserRoleAssign

# You can add more schemas here and they will be available under app.schemas