from sqlalchemy import Column, Integer, String, DateTime, Text, func
from sqlalchemy.dialects.mysql import TINYINT
from app.database import Base

class SysRole(Base):
    __tablename__ = "sys_role"

    id = Column(Integer, primary_key=True, index=True)
    role_name = Column(String(50), nullable=False, comment="角色名称")
    role_code = Column(String(50), nullable=False, unique=True, comment="角色编码")
    description = Column(String(255), comment="角色描述")
    status = Column(TINYINT, default=1, comment="状态（1-启用，0-禁用）")
    create_time = Column(DateTime, default=func.now(), comment="创建时间")
    update_time = Column(DateTime, default=func.now(), onupdate=func.now(), comment="更新时间")
    deleted = Column(TINYINT, default=0, comment="是否删除")

class SysPermission(Base):
    __tablename__ = "sys_permission"

    id = Column(Integer, primary_key=True, index=True)
    name = Column(String(50), nullable=False, comment="权限名称")
    code = Column(String(50), nullable=False, unique=True, comment="权限编码")
    type = Column(TINYINT, nullable=False, comment="权限类型（1-菜单，2-按钮）")
    parent_id = Column(Integer, default=0, comment="父权限ID")
    path = Column(String(100), comment="路径")
    component = Column(String(100), comment="组件")
    icon = Column(String(50), comment="图标")
    sort = Column(Integer, default=0, comment="排序")
    status = Column(TINYINT, default=1, comment="状态（1-启用，0-禁用）")
    create_time = Column(DateTime, default=func.now(), comment="创建时间")
    update_time = Column(DateTime, default=func.now(), onupdate=func.now(), comment="更新时间")
    deleted = Column(TINYINT, default=0, comment="是否删除")

class SysRolePermission(Base):
    __tablename__ = "sys_role_permission"

    id = Column(Integer, primary_key=True, index=True)
    role_id = Column(Integer, nullable=False, comment="角色ID")
    permission_id = Column(Integer, nullable=False, comment="权限ID")
    create_time = Column(DateTime, default=func.now(), comment="创建时间")

class SysUserRole(Base):
    __tablename__ = "sys_user_role"

    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, nullable=False, comment="用户ID")
    role_id = Column(Integer, nullable=False, comment="角色ID")
    create_time = Column(DateTime, default=func.now(), comment="创建时间")
