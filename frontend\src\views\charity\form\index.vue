<template>
  <div class="charity-form">
    <el-card class="box-card">
      <template #header>
        <div class="card-header">
          <span>{{ isEdit ? '编辑活动' : '创建活动' }}</span>
          <el-button @click="goBack">返回列表</el-button>
        </div>
      </template>

      <el-form
        ref="formRef"
        :model="form"
        :rules="rules"
        label-width="120px"
        class="charity-form-content"
      >
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="活动标题" prop="title">
              <el-input v-model="form.title" placeholder="请输入活动标题" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="活动分类" prop="category">
              <el-select v-model="form.category" placeholder="请选择活动分类" style="width: 100%">
                <el-option label="环保公益" value="环保" />
                <el-option label="教育助学" value="教育" />
                <el-option label="扶贫济困" value="扶贫" />
                <el-option label="关爱老人" value="关爱" />
                <el-option label="儿童关怀" value="儿童" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="活动状态" prop="status">
              <el-select v-model="form.status" placeholder="请选择活动状态" style="width: 100%">
                <el-option label="即将开始" value="upcoming" />
                <el-option label="进行中" value="ongoing" />
                <el-option label="已结束" value="finished" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="最大参与人数" prop="max_participants">
              <el-input-number v-model="form.max_participants" :min="1" :max="10000" style="width: 100%" />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="活动地点" prop="location">
              <el-input v-model="form.location" placeholder="请输入活动地点" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="活动时间" prop="time">
              <el-input v-model="form.time" placeholder="例如：2023-12-25 09:00-17:00" />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="组织者" prop="organizer">
              <el-input v-model="form.organizer" placeholder="请输入组织者名称" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="联系方式" prop="contact">
              <el-input v-model="form.contact" placeholder="请输入联系方式" />
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item label="活动图片" prop="image">
          <el-input v-model="form.image" placeholder="请输入图片URL" />
        </el-form-item>

        <el-form-item label="活动描述" prop="description">
          <el-input
            v-model="form.description"
            type="textarea"
            :rows="3"
            placeholder="请输入活动简要描述"
          />
        </el-form-item>

        <el-form-item label="活动内容" prop="content">
          <el-input
            v-model="form.content"
            type="textarea"
            :rows="6"
            placeholder="请输入详细的活动内容，支持HTML格式"
          />
        </el-form-item>

        <el-form-item label="参与要求" prop="requirements">
          <el-input
            v-model="form.requirements"
            type="textarea"
            :rows="3"
            placeholder="请输入参与活动的要求"
          />
        </el-form-item>

        <el-form-item label="活动须知" prop="notices">
          <el-input
            v-model="form.notices"
            type="textarea"
            :rows="3"
            placeholder="请输入活动须知和注意事项"
          />
        </el-form-item>

        <el-form-item>
          <el-button type="primary" @click="handleSubmit" :loading="loading">
            {{ isEdit ? '更新' : '创建' }}
          </el-button>
          <el-button @click="handleReset">重置</el-button>
          <el-button @click="goBack">取消</el-button>
        </el-form-item>
      </el-form>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { ElMessage } from 'element-plus'
import type { FormInstance } from 'element-plus'
import { createCharity, updateCharity, getCharity } from '@/api/charity'

const router = useRouter()
const route = useRoute()
const formRef = ref<FormInstance>()

// 响应式数据
const loading = ref(false)
const isEdit = ref(false)
const charityId = ref<number | null>(null)

// 表单数据
const form = reactive({
  title: '',
  category: '',
  status: 'upcoming',
  max_participants: 100,
  location: '',
  time: '',
  organizer: '',
  contact: '',
  image: '',
  description: '',
  content: '',
  requirements: '',
  notices: ''
})

// 表单验证规则
const rules = {
  title: [
    { required: true, message: '请输入活动标题', trigger: 'blur' }
  ],
  category: [
    { required: true, message: '请选择活动分类', trigger: 'change' }
  ],
  status: [
    { required: true, message: '请选择活动状态', trigger: 'change' }
  ],
  location: [
    { required: true, message: '请输入活动地点', trigger: 'blur' }
  ],
  time: [
    { required: true, message: '请输入活动时间', trigger: 'blur' }
  ],
  organizer: [
    { required: true, message: '请输入组织者', trigger: 'blur' }
  ],
  contact: [
    { required: true, message: '请输入联系方式', trigger: 'blur' }
  ]
}

// 初始化数据
const initData = async () => {
  const id = route.query.id
  if (id) {
    isEdit.value = true
    charityId.value = Number(id)
    await fetchCharityData(charityId.value)
  }
}

// 获取活动数据
const fetchCharityData = async (id: number) => {
  try {
    const response = await getCharity(id)
    Object.keys(form).forEach(key => {
      if (response[key] !== undefined) {
        form[key] = response[key]
      }
    })
  } catch (error) {
    ElMessage.error('获取活动数据失败')
    console.error(error)
  }
}

// 提交表单
const handleSubmit = async () => {
  if (!formRef.value) return
  
  try {
    await formRef.value.validate()
    loading.value = true
    
    if (isEdit.value && charityId.value) {
      await updateCharity(charityId.value, form)
      ElMessage.success('更新成功')
    } else {
      await createCharity(form)
      ElMessage.success('创建成功')
    }
    
    goBack()
  } catch (error) {
    if (error !== false) { // 不是表单验证错误
      ElMessage.error(isEdit.value ? '更新失败' : '创建失败')
      console.error(error)
    }
  } finally {
    loading.value = false
  }
}

// 重置表单
const handleReset = () => {
  if (!formRef.value) return
  formRef.value.resetFields()
}

// 返回列表
const goBack = () => {
  router.push('/charity/list')
}

// 初始化
onMounted(() => {
  initData()
})
</script>

<style scoped>
.charity-form {
  padding: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.charity-form-content {
  max-width: 800px;
}
</style>
