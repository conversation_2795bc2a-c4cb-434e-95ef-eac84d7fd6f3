<template>
  <div class="question-list">
    <el-card class="box-card">
      <template #header>
        <div class="card-header">
          <span>题目管理</span>
          <el-button type="primary" @click="handleCreate">
            <el-icon><Plus /></el-icon>
            新建题目
          </el-button>
        </div>
      </template>

      <!-- 搜索栏 -->
      <div class="search-bar">
        <el-form :inline="true" :model="searchForm" class="search-form">
          <el-form-item label="题目类型">
            <el-select v-model="searchForm.type" placeholder="请选择类型" clearable>
              <el-option label="单选题" value="single" />
              <el-option label="多选题" value="multiple" />
              <el-option label="判断题" value="judge" />
            </el-select>
          </el-form-item>
          <el-form-item label="题目分类">
            <el-select v-model="searchForm.category" placeholder="请选择分类" clearable>
              <el-option label="基础知识" value="basic" />
              <el-option label="法律法规" value="law" />
              <el-option label="安全常识" value="safety" />
              <el-option label="环保知识" value="environment" />
              <el-option label="文化素养" value="culture" />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="handleSearch">搜索</el-button>
            <el-button @click="handleReset">重置</el-button>
          </el-form-item>
        </el-form>
      </div>

      <!-- 数据表格 -->
      <el-table :data="tableData" style="width: 100%" v-loading="loading">
        <el-table-column prop="id" label="ID" width="80" />
        <el-table-column prop="question" label="题目内容" min-width="300" show-overflow-tooltip />
        <el-table-column prop="type" label="题目类型" width="100">
          <template #default="scope">
            <el-tag :type="getTypeColor(scope.row.type)">
              {{ getTypeText(scope.row.type) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="category" label="分类" width="120" />
        <el-table-column prop="correct_answer" label="正确答案" width="120" />
        <el-table-column prop="create_time" label="创建时间" width="180">
          <template #default="scope">
            {{ formatDate(scope.row.create_time) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="scope">
            <el-button size="small" @click="handleView(scope.row)">查看</el-button>
            <el-button size="small" type="primary" @click="handleEdit(scope.row)">编辑</el-button>
            <el-button size="small" type="danger" @click="handleDelete(scope.row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination">
        <el-pagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.size"
          :page-sizes="[10, 20, 50, 100]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 查看详情对话框 -->
    <el-dialog v-model="dialogVisible" title="题目详情" width="60%">
      <div v-if="currentItem">
        <el-descriptions :column="1" border>
          <el-descriptions-item label="题目内容">{{ currentItem.question }}</el-descriptions-item>
          <el-descriptions-item label="题目类型">
            <el-tag :type="getTypeColor(currentItem.type)">
              {{ getTypeText(currentItem.type) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="分类">{{ currentItem.category }}</el-descriptions-item>
          <el-descriptions-item label="选项" v-if="currentItem.options">
            <div v-for="option in currentItem.options" :key="option.id" class="option-item">
              <strong>{{ option.id }}:</strong> {{ option.text }}
            </div>
          </el-descriptions-item>
          <el-descriptions-item label="正确答案">{{ currentItem.correct_answer }}</el-descriptions-item>
          <el-descriptions-item label="解析" v-if="currentItem.explanation">
            {{ currentItem.explanation }}
          </el-descriptions-item>
        </el-descriptions>
      </div>
    </el-dialog>

    <!-- 新建/编辑题目对话框 -->
    <el-dialog
      v-model="formDialogVisible"
      :title="isEdit ? '编辑题目' : '新建题目'"
      width="70%"
      :close-on-click-modal="false"
    >
      <el-form
        ref="formRef"
        :model="form"
        :rules="rules"
        label-width="120px"
      >
        <el-form-item label="题目类型" prop="type">
          <el-select v-model="form.type" placeholder="请选择题目类型" @change="handleTypeChange">
            <el-option label="单选题" value="single" />
            <el-option label="多选题" value="multiple" />
            <el-option label="判断题" value="judge" />
          </el-select>
        </el-form-item>

        <el-form-item label="题目分类" prop="category">
          <el-select v-model="form.category" placeholder="请选择分类">
            <el-option label="基础知识" value="basic" />
            <el-option label="法律法规" value="law" />
            <el-option label="安全常识" value="safety" />
            <el-option label="环保知识" value="environment" />
            <el-option label="文化素养" value="culture" />
          </el-select>
        </el-form-item>

        <el-form-item label="题目内容" prop="question">
          <el-input
            v-model="form.question"
            type="textarea"
            :rows="3"
            placeholder="请输入题目内容"
          />
        </el-form-item>

        <!-- 选项设置 -->
        <el-form-item label="选项设置" v-if="form.type !== 'judge'" prop="options">
          <div v-for="(option, index) in form.options" :key="index" class="option-input">
            <el-input
              v-model="option.text"
              :placeholder="`选项 ${option.id}`"
              style="margin-bottom: 10px"
            >
              <template #prepend>{{ option.id }}</template>
              <template #append>
                <el-button
                  v-if="form.options.length > 2"
                  @click="removeOption(index)"
                  type="danger"
                  size="small"
                >
                  删除
                </el-button>
              </template>
            </el-input>
          </div>
          <el-button @click="addOption" type="primary" size="small">添加选项</el-button>
        </el-form-item>

        <el-form-item label="正确答案" prop="correct_answer">
          <el-select
            v-if="form.type === 'single'"
            v-model="form.correct_answer"
            placeholder="请选择正确答案"
          >
            <el-option
              v-for="option in form.options"
              :key="option.id"
              :label="`${option.id}: ${option.text}`"
              :value="option.id"
            />
          </el-select>
          <el-select
            v-else-if="form.type === 'multiple'"
            v-model="form.correct_answer"
            multiple
            placeholder="请选择正确答案（可多选）"
          >
            <el-option
              v-for="option in form.options"
              :key="option.id"
              :label="`${option.id}: ${option.text}`"
              :value="option.id"
            />
          </el-select>
          <el-select
            v-else-if="form.type === 'judge'"
            v-model="form.correct_answer"
            placeholder="请选择正确答案"
          >
            <el-option label="正确" value="A" />
            <el-option label="错误" value="B" />
          </el-select>
        </el-form-item>

        <el-form-item label="答案解析" prop="explanation">
          <el-input
            v-model="form.explanation"
            type="textarea"
            :rows="3"
            placeholder="请输入答案解析（可选）"
          />
        </el-form-item>
      </el-form>
      
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="formDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleSubmit" :loading="submitLoading">
            {{ isEdit ? '更新' : '创建' }}
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus } from '@element-plus/icons-vue'
import type { FormInstance } from 'element-plus'
import { getQuestions, createQuestion, updateQuestion, deleteQuestion } from '@/api/question'

// 响应式数据
const loading = ref(false)
const submitLoading = ref(false)
const tableData = ref([])
const dialogVisible = ref(false)
const formDialogVisible = ref(false)
const isEdit = ref(false)
const currentItem = ref(null)
const currentId = ref<number | null>(null)
const formRef = ref<FormInstance>()

// 搜索表单
const searchForm = reactive({
  type: '',
  category: ''
})

// 题目表单
const form = reactive({
  type: '',
  category: '',
  question: '',
  options: [
    { id: 'A', text: '' },
    { id: 'B', text: '' }
  ],
  correct_answer: '',
  explanation: ''
})

// 分页
const pagination = reactive({
  page: 1,
  size: 10,
  total: 0
})

// 表单验证规则
const rules = {
  type: [{ required: true, message: '请选择题目类型', trigger: 'change' }],
  category: [{ required: true, message: '请选择分类', trigger: 'change' }],
  question: [{ required: true, message: '请输入题目内容', trigger: 'blur' }],
  correct_answer: [{ required: true, message: '请选择正确答案', trigger: 'change' }]
}

// 工具方法
const getTypeColor = (type: string) => {
  const colorMap = {
    single: 'success',
    multiple: 'warning',
    judge: 'info'
  }
  return colorMap[type] || 'info'
}

const getTypeText = (type: string) => {
  const textMap = {
    single: '单选题',
    multiple: '多选题',
    judge: '判断题'
  }
  return textMap[type] || type
}

const formatDate = (dateString: string) => {
  if (!dateString) return '-'
  return new Date(dateString).toLocaleString()
}

// 获取数据
const fetchData = async () => {
  loading.value = true
  try {
    const params = {
      skip: (pagination.page - 1) * pagination.size,
      limit: pagination.size,
      ...searchForm
    }
    const response = await getQuestions(params)
    tableData.value = response || []
    pagination.total = tableData.value.length
  } catch (error) {
    ElMessage.error('获取数据失败')
    console.error(error)
  } finally {
    loading.value = false
  }
}

// 事件处理
const handleSearch = () => {
  pagination.page = 1
  fetchData()
}

const handleReset = () => {
  Object.keys(searchForm).forEach(key => {
    searchForm[key] = ''
  })
  pagination.page = 1
  fetchData()
}

const handleCreate = () => {
  isEdit.value = false
  currentId.value = null
  resetForm()
  formDialogVisible.value = true
}

const handleView = (row: any) => {
  currentItem.value = row
  dialogVisible.value = true
}

const handleEdit = (row: any) => {
  isEdit.value = true
  currentId.value = row.id
  
  // 填充表单数据
  form.type = row.type
  form.category = row.category
  form.question = row.question
  form.options = row.options || [{ id: 'A', text: '' }, { id: 'B', text: '' }]
  form.correct_answer = row.correct_answer
  form.explanation = row.explanation || ''
  
  formDialogVisible.value = true
}

const handleDelete = async (row: any) => {
  try {
    await ElMessageBox.confirm('确定要删除这个题目吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    
    await deleteQuestion(row.id)
    ElMessage.success('删除成功')
    fetchData()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败')
    }
  }
}

const handleTypeChange = (type: string) => {
  if (type === 'judge') {
    form.options = [
      { id: 'A', text: '正确' },
      { id: 'B', text: '错误' }
    ]
  } else {
    form.options = [
      { id: 'A', text: '' },
      { id: 'B', text: '' }
    ]
  }
  form.correct_answer = ''
}

const addOption = () => {
  const nextId = String.fromCharCode(65 + form.options.length) // A, B, C, D...
  form.options.push({ id: nextId, text: '' })
}

const removeOption = (index: number) => {
  form.options.splice(index, 1)
  // 重新分配ID
  form.options.forEach((option, idx) => {
    option.id = String.fromCharCode(65 + idx)
  })
}

const resetForm = () => {
  form.type = ''
  form.category = ''
  form.question = ''
  form.options = [{ id: 'A', text: '' }, { id: 'B', text: '' }]
  form.correct_answer = ''
  form.explanation = ''
}

const handleSubmit = async () => {
  if (!formRef.value) return
  
  try {
    await formRef.value.validate()
    submitLoading.value = true
    
    const submitData = {
      ...form,
      correct_answer: form.type === 'multiple' ? JSON.stringify(form.correct_answer) : form.correct_answer
    }
    
    if (isEdit.value && currentId.value) {
      await updateQuestion(currentId.value, submitData)
      ElMessage.success('更新成功')
    } else {
      await createQuestion(submitData)
      ElMessage.success('创建成功')
    }
    
    formDialogVisible.value = false
    fetchData()
  } catch (error) {
    if (error !== false) {
      ElMessage.error(isEdit.value ? '更新失败' : '创建失败')
      console.error(error)
    }
  } finally {
    submitLoading.value = false
  }
}

const handleSizeChange = (size: number) => {
  pagination.size = size
  pagination.page = 1
  fetchData()
}

const handleCurrentChange = (page: number) => {
  pagination.page = page
  fetchData()
}

// 初始化
onMounted(() => {
  fetchData()
})
</script>

<style scoped>
.question-list {
  padding: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.search-bar {
  margin-bottom: 20px;
}

.search-form {
  background-color: #f5f5f5;
  padding: 20px;
  border-radius: 4px;
}

.pagination {
  margin-top: 20px;
  text-align: right;
}

.option-item {
  margin-bottom: 5px;
}

.option-input {
  margin-bottom: 10px;
}
</style>
