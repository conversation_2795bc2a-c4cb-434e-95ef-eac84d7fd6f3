from sqlalchemy import Column, Integer, String, DateTime, Text, JSON, func
from sqlalchemy.dialects.mysql import TINYINT
from app.database import Base

class Question(Base):
    __tablename__ = "question"

    id = Column(Integer, primary_key=True, index=True)
    type = Column(String(20), nullable=False, comment="题目类型（single-单选题，multiple-多选题，judge-判断题）")
    category = Column(String(64), comment="分类")
    question = Column(Text, nullable=False, comment="题目内容")
    options = Column(JSON, comment="选项，JSON格式")
    correct_answer = Column(String(255), nullable=False, comment="正确答案，多选题为JSON数组")
    explanation = Column(Text, comment="解析")
    create_time = Column(DateTime, default=func.now(), comment="创建时间")
    update_time = Column(DateTime, default=func.now(), onupdate=func.now(), comment="更新时间")
    deleted = Column(TINYINT, default=0, comment="是否删除")

class Category(Base):
    __tablename__ = "category"

    id = Column(Integer, primary_key=True, index=True)
    name = Column(String(64), nullable=False, comment="分类名称")
    create_time = Column(DateTime, default=func.now(), comment="创建时间")
    update_time = Column(DateTime, default=func.now(), onupdate=func.now(), comment="更新时间")
    deleted = Column(TINYINT, default=0, comment="是否删除")

class QuizHistory(Base):
    __tablename__ = "quiz_history"

    id = Column(Integer, primary_key=True, index=True)
    open_id = Column(String(64), nullable=False, comment="用户OpenID")
    score = Column(Integer, default=0, comment="得分")
    total = Column(Integer, default=0, comment="总题数")
    mode = Column(String(20), default="practice", comment="模式（practice-练习模式）")
    mode_name = Column(String(64), comment="模式名称")
    timestamp = Column(Integer, comment="答题时间戳")
    time_str = Column(String(20), comment="答题时间字符串")
    create_time = Column(DateTime, default=func.now(), comment="创建时间")
    update_time = Column(DateTime, default=func.now(), onupdate=func.now(), comment="更新时间")
    deleted = Column(TINYINT, default=0, comment="是否删除")
