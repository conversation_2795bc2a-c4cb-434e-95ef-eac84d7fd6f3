<template>
  <div class="quiz-history">
    <el-card class="box-card">
      <template #header>
        <div class="card-header">
          <span>答题历史记录</span>
        </div>
      </template>

      <!-- 搜索栏 -->
      <div class="search-bar">
        <el-form :inline="true" :model="searchForm" class="search-form">
          <el-form-item label="用户OpenID">
            <el-input v-model="searchForm.open_id" placeholder="请输入用户OpenID" clearable />
          </el-form-item>
          <el-form-item label="答题模式">
            <el-select v-model="searchForm.mode" placeholder="请选择模式" clearable>
              <el-option label="练习模式" value="practice" />
              <el-option label="考试模式" value="exam" />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="handleSearch">搜索</el-button>
            <el-button @click="handleReset">重置</el-button>
          </el-form-item>
        </el-form>
      </div>

      <!-- 数据表格 -->
      <el-table :data="tableData" style="width: 100%" v-loading="loading">
        <el-table-column prop="id" label="ID" width="80" />
        <el-table-column prop="open_id" label="用户OpenID" min-width="200" show-overflow-tooltip />
        <el-table-column prop="mode_name" label="答题模式" width="150" />
        <el-table-column prop="score" label="得分" width="80">
          <template #default="scope">
            <el-tag :type="getScoreType(scope.row.score, scope.row.total)">
              {{ scope.row.score }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="total" label="总题数" width="80" />
        <el-table-column label="正确率" width="100">
          <template #default="scope">
            <span :class="getAccuracyClass(scope.row.score, scope.row.total)">
              {{ getAccuracy(scope.row.score, scope.row.total) }}%
            </span>
          </template>
        </el-table-column>
        <el-table-column prop="time_str" label="答题时间" width="180" />
        <el-table-column prop="create_time" label="记录时间" width="180">
          <template #default="scope">
            {{ formatDate(scope.row.create_time) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="100" fixed="right">
          <template #default="scope">
            <el-button size="small" @click="handleView(scope.row)">查看</el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination">
        <el-pagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.size"
          :page-sizes="[10, 20, 50, 100]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 查看详情对话框 -->
    <el-dialog v-model="dialogVisible" title="答题记录详情" width="50%">
      <div v-if="currentItem">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="用户OpenID" :span="2">{{ currentItem.open_id }}</el-descriptions-item>
          <el-descriptions-item label="答题模式">{{ currentItem.mode_name }}</el-descriptions-item>
          <el-descriptions-item label="答题时间">{{ currentItem.time_str }}</el-descriptions-item>
          <el-descriptions-item label="得分">
            <el-tag :type="getScoreType(currentItem.score, currentItem.total)">
              {{ currentItem.score }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="总题数">{{ currentItem.total }}</el-descriptions-item>
          <el-descriptions-item label="正确率" :span="2">
            <span :class="getAccuracyClass(currentItem.score, currentItem.total)">
              {{ getAccuracy(currentItem.score, currentItem.total) }}%
            </span>
          </el-descriptions-item>
          <el-descriptions-item label="记录时间" :span="2">
            {{ formatDate(currentItem.create_time) }}
          </el-descriptions-item>
        </el-descriptions>

        <!-- 成绩分析 -->
        <div class="score-analysis" style="margin-top: 20px;">
          <h4>成绩分析</h4>
          <el-row :gutter="20">
            <el-col :span="8">
              <el-statistic title="正确题数" :value="currentItem.score" />
            </el-col>
            <el-col :span="8">
              <el-statistic title="错误题数" :value="currentItem.total - currentItem.score" />
            </el-col>
            <el-col :span="8">
              <el-statistic 
                title="正确率" 
                :value="getAccuracy(currentItem.score, currentItem.total)" 
                suffix="%" 
              />
            </el-col>
          </el-row>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { getQuizHistories } from '@/api/question'

// 响应式数据
const loading = ref(false)
const tableData = ref([])
const dialogVisible = ref(false)
const currentItem = ref(null)

// 搜索表单
const searchForm = reactive({
  open_id: '',
  mode: ''
})

// 分页
const pagination = reactive({
  page: 1,
  size: 10,
  total: 0
})

// 工具方法
const formatDate = (dateString: string) => {
  if (!dateString) return '-'
  return new Date(dateString).toLocaleString()
}

const getAccuracy = (score: number, total: number) => {
  if (total === 0) return 0
  return Math.round((score / total) * 100)
}

const getScoreType = (score: number, total: number) => {
  const accuracy = getAccuracy(score, total)
  if (accuracy >= 90) return 'success'
  if (accuracy >= 70) return 'warning'
  return 'danger'
}

const getAccuracyClass = (score: number, total: number) => {
  const accuracy = getAccuracy(score, total)
  if (accuracy >= 90) return 'text-success'
  if (accuracy >= 70) return 'text-warning'
  return 'text-danger'
}

// 获取数据
const fetchData = async () => {
  loading.value = true
  try {
    const params = {
      skip: (pagination.page - 1) * pagination.size,
      limit: pagination.size,
      ...searchForm
    }
    const response = await getQuizHistories(params)
    tableData.value = response || []
    pagination.total = tableData.value.length
  } catch (error) {
    ElMessage.error('获取数据失败')
    console.error(error)
  } finally {
    loading.value = false
  }
}

// 事件处理
const handleSearch = () => {
  pagination.page = 1
  fetchData()
}

const handleReset = () => {
  Object.keys(searchForm).forEach(key => {
    searchForm[key] = ''
  })
  pagination.page = 1
  fetchData()
}

const handleView = (row: any) => {
  currentItem.value = row
  dialogVisible.value = true
}

const handleSizeChange = (size: number) => {
  pagination.size = size
  pagination.page = 1
  fetchData()
}

const handleCurrentChange = (page: number) => {
  pagination.page = page
  fetchData()
}

// 初始化
onMounted(() => {
  fetchData()
})
</script>

<style scoped>
.quiz-history {
  padding: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.search-bar {
  margin-bottom: 20px;
}

.search-form {
  background-color: #f5f5f5;
  padding: 20px;
  border-radius: 4px;
}

.pagination {
  margin-top: 20px;
  text-align: right;
}

.text-success {
  color: #67c23a;
  font-weight: bold;
}

.text-warning {
  color: #e6a23c;
  font-weight: bold;
}

.text-danger {
  color: #f56c6c;
  font-weight: bold;
}

.score-analysis {
  border-top: 1px solid #ebeef5;
  padding-top: 20px;
}
</style>
