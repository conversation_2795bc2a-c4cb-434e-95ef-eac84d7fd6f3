from typing import List
from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session
from app import crud, schemas
from app.database import get_db

router = APIRouter()

# 题目相关接口
@router.get("/", response_model=List[schemas.Question])
def get_questions(
    skip: int = 0,
    limit: int = 100,
    category: str = None,
    question_type: str = None,
    db: Session = Depends(get_db)
):
    """
    获取题目列表
    """
    if category:
        questions = crud.question.get_questions_by_category(db, category=category, skip=skip, limit=limit)
    elif question_type:
        questions = crud.question.get_questions_by_type(db, question_type=question_type, skip=skip, limit=limit)
    else:
        questions = crud.question.get_questions(db, skip=skip, limit=limit)
    return questions

@router.get("/{question_id}", response_model=schemas.Question)
def get_question(question_id: int, db: Session = Depends(get_db)):
    """
    获取单个题目详情
    """
    question = crud.question.get_question(db, question_id=question_id)
    if not question:
        raise HTTPException(status_code=404, detail="题目不存在")
    return question

@router.post("/", response_model=schemas.Question)
def create_question(question: schemas.QuestionCreate, db: Session = Depends(get_db)):
    """
    创建题目
    """
    return crud.question.create_question(db=db, question=question)

@router.put("/{question_id}", response_model=schemas.Question)
def update_question(
    question_id: int,
    question: schemas.QuestionUpdate,
    db: Session = Depends(get_db)
):
    """
    更新题目
    """
    db_question = crud.question.update_question(db=db, question_id=question_id, question=question)
    if not db_question:
        raise HTTPException(status_code=404, detail="题目不存在")
    return db_question

@router.delete("/{question_id}")
def delete_question(question_id: int, db: Session = Depends(get_db)):
    """
    删除题目
    """
    success = crud.question.delete_question(db=db, question_id=question_id)
    if not success:
        raise HTTPException(status_code=404, detail="题目不存在")
    return {"message": "删除成功"}

# 题目分类相关接口
@router.get("/categories/", response_model=List[schemas.Category])
def get_categories(
    skip: int = 0,
    limit: int = 100,
    db: Session = Depends(get_db)
):
    """
    获取题目分类列表
    """
    return crud.question.get_categories(db, skip=skip, limit=limit)

@router.get("/categories/{category_id}", response_model=schemas.Category)
def get_category(category_id: int, db: Session = Depends(get_db)):
    """
    获取单个题目分类详情
    """
    category = crud.question.get_category(db, category_id=category_id)
    if not category:
        raise HTTPException(status_code=404, detail="分类不存在")
    return category

@router.post("/categories/", response_model=schemas.Category)
def create_category(category: schemas.CategoryCreate, db: Session = Depends(get_db)):
    """
    创建题目分类
    """
    return crud.question.create_category(db=db, category=category)

@router.put("/categories/{category_id}", response_model=schemas.Category)
def update_category(
    category_id: int,
    category: schemas.CategoryUpdate,
    db: Session = Depends(get_db)
):
    """
    更新题目分类
    """
    db_category = crud.question.update_category(db=db, category_id=category_id, category=category)
    if not db_category:
        raise HTTPException(status_code=404, detail="分类不存在")
    return db_category

@router.delete("/categories/{category_id}")
def delete_category(category_id: int, db: Session = Depends(get_db)):
    """
    删除题目分类
    """
    success = crud.question.delete_category(db=db, category_id=category_id)
    if not success:
        raise HTTPException(status_code=404, detail="分类不存在")
    return {"message": "删除成功"}

# 答题历史相关接口
@router.get("/quiz-history/", response_model=List[schemas.QuizHistory])
def get_quiz_histories(
    skip: int = 0,
    limit: int = 100,
    open_id: str = None,
    db: Session = Depends(get_db)
):
    """
    获取答题历史列表
    """
    if open_id:
        histories = crud.question.get_quiz_histories_by_user(db, open_id=open_id, skip=skip, limit=limit)
    else:
        histories = crud.question.get_quiz_histories(db, skip=skip, limit=limit)
    return histories

@router.post("/quiz-history/", response_model=schemas.QuizHistory)
def create_quiz_history(quiz_history: schemas.QuizHistoryCreate, db: Session = Depends(get_db)):
    """
    创建答题历史记录
    """
    return crud.question.create_quiz_history(db=db, quiz_history=quiz_history)
