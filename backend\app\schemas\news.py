from pydantic import BaseModel
from typing import Optional
from datetime import datetime

# 新闻基础模式
class NewsInfoBase(BaseModel):
    title: str
    description: Optional[str] = None
    content: Optional[str] = None
    read_count: Optional[str] = "0"
    comments: Optional[str] = "0"
    publish_date: Optional[str] = None
    author: Optional[str] = None
    category: Optional[str] = None
    image: Optional[str] = None

# 创建新闻
class NewsInfoCreate(NewsInfoBase):
    pass

# 更新新闻
class NewsInfoUpdate(NewsInfoBase):
    title: Optional[str] = None

# 数据库中的新闻
class NewsInfoInDBBase(NewsInfoBase):
    id: int
    create_time: Optional[datetime] = None
    update_time: Optional[datetime] = None

    class Config:
        from_attributes = True

# 返回给客户端的新闻
class NewsInfo(NewsInfoInDBBase):
    pass
