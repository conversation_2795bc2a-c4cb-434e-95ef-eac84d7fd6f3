from typing import List
from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.orm import Session
from app import crud, schemas
from app.database import get_db

router = APIRouter()

# 用户管理相关接口
@router.get("/", response_model=List[schemas.Admin])
def get_users(
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=1000),
    username: str = Query(None),
    email: str = Query(None),
    real_name: str = Query(None),
    status: int = Query(None),
    db: Session = Depends(get_db)
):
    """
    获取用户列表
    """
    query = schemas.AdminQuery(
        username=username,
        email=email,
        real_name=real_name,
        status=status,
        skip=skip,
        limit=limit
    )
    
    if any([username, email, real_name, status is not None]):
        users = crud.admin.get_admins_by_query(db, query=query)
    else:
        users = crud.admin.get_admins(db, skip=skip, limit=limit)
    
    # 为每个用户添加角色信息
    result = []
    for user in users:
        user_roles = crud.role.get_user_roles(db, user.id)
        user_dict = schemas.Admin.from_orm(user).dict()
        user_dict['roles'] = [role.name for role in user_roles]
        result.append(user_dict)
    
    return result

@router.get("/{user_id}", response_model=schemas.Admin)
def get_user(user_id: int, db: Session = Depends(get_db)):
    """
    获取单个用户详情
    """
    user = crud.admin.get_admin(db, admin_id=user_id)
    if not user:
        raise HTTPException(status_code=404, detail="用户不存在")
    
    # 添加角色信息
    user_roles = crud.role.get_user_roles(db, user.id)
    user_dict = schemas.Admin.from_orm(user).dict()
    user_dict['roles'] = [role.name for role in user_roles]
    
    return user_dict

@router.post("/", response_model=schemas.Admin)
def create_user(user: schemas.AdminCreate, db: Session = Depends(get_db)):
    """
    创建用户
    """
    # 检查用户名是否已存在
    existing_user = crud.admin.get_admin_by_username(db, username=user.username)
    if existing_user:
        raise HTTPException(status_code=400, detail="用户名已存在")
    
    return crud.admin.create_admin(db=db, obj_in=user)

@router.put("/{user_id}", response_model=schemas.Admin)
def update_user(
    user_id: int,
    user: schemas.AdminUpdate,
    db: Session = Depends(get_db)
):
    """
    更新用户
    """
    # 检查用户是否存在
    existing_user = crud.admin.get_admin(db, admin_id=user_id)
    if not existing_user:
        raise HTTPException(status_code=404, detail="用户不存在")
    
    # 如果更新用户名，检查是否重复
    if user.username and user.username != existing_user.username:
        username_exists = crud.admin.get_admin_by_username(db, username=user.username)
        if username_exists:
            raise HTTPException(status_code=400, detail="用户名已存在")
    
    updated_user = crud.admin.update_admin(db=db, admin_id=user_id, obj_in=user)
    if not updated_user:
        raise HTTPException(status_code=404, detail="用户不存在")
    
    return updated_user

@router.delete("/{user_id}")
def delete_user(user_id: int, db: Session = Depends(get_db)):
    """
    删除用户
    """
    success = crud.admin.delete_admin(db=db, admin_id=user_id)
    if not success:
        raise HTTPException(status_code=404, detail="用户不存在")
    return {"message": "删除成功"}

@router.post("/{user_id}/roles")
def assign_user_roles(
    user_id: int,
    role_assign: schemas.UserRoleAssign,
    db: Session = Depends(get_db)
):
    """
    为用户分配角色
    """
    # 检查用户是否存在
    user = crud.admin.get_admin(db, admin_id=user_id)
    if not user:
        raise HTTPException(status_code=404, detail="用户不存在")
    
    # 检查角色是否都存在
    for role_id in role_assign.role_ids:
        role = crud.role.get_role(db, role_id=role_id)
        if not role:
            raise HTTPException(status_code=404, detail=f"角色ID {role_id} 不存在")
    
    # 分配角色
    role_assign.user_id = user_id
    success = crud.role.assign_user_roles(db=db, user_role_assign=role_assign)
    if not success:
        raise HTTPException(status_code=500, detail="角色分配失败")
    
    return {"message": "角色分配成功"}

@router.get("/{user_id}/roles", response_model=List[schemas.Role])
def get_user_roles(user_id: int, db: Session = Depends(get_db)):
    """
    获取用户的角色列表
    """
    # 检查用户是否存在
    user = crud.admin.get_admin(db, admin_id=user_id)
    if not user:
        raise HTTPException(status_code=404, detail="用户不存在")
    
    return crud.role.get_user_roles(db, user_id=user_id)

@router.get("/stats/count")
def get_user_stats(db: Session = Depends(get_db)):
    """
    获取用户统计信息
    """
    total_users = crud.admin.get_admin_count(db)
    active_users = len(crud.admin.get_admins_by_query(db, schemas.AdminQuery(status=1, limit=1000)))
    inactive_users = len(crud.admin.get_admins_by_query(db, schemas.AdminQuery(status=0, limit=1000)))
    
    return {
        "total_users": total_users,
        "active_users": active_users,
        "inactive_users": inactive_users
    }
