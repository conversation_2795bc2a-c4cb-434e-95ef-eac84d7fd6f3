from typing import List
from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session
from app import crud, schemas
from app.database import get_db

router = APIRouter()

# 新闻相关接口
@router.get("/", response_model=List[schemas.NewsInfo])
def get_news_list(
    skip: int = 0,
    limit: int = 100,
    category: str = None,
    db: Session = Depends(get_db)
):
    """
    获取新闻列表
    """
    if category:
        news_list = crud.news.get_news_by_category(db, category=category, skip=skip, limit=limit)
    else:
        news_list = crud.news.get_news_list(db, skip=skip, limit=limit)
    return news_list

@router.get("/{news_id}", response_model=schemas.NewsInfo)
def get_news(news_id: int, db: Session = Depends(get_db)):
    """
    获取单个新闻详情
    """
    news = crud.news.get_news(db, news_id=news_id)
    if not news:
        raise HTTPException(status_code=404, detail="新闻不存在")
    
    # 增加阅读量
    crud.news.increment_read_count(db, news_id=news_id)
    
    return news

@router.post("/", response_model=schemas.NewsInfo)
def create_news(news: schemas.NewsInfoCreate, db: Session = Depends(get_db)):
    """
    创建新闻
    """
    return crud.news.create_news(db=db, news=news)

@router.put("/{news_id}", response_model=schemas.NewsInfo)
def update_news(
    news_id: int,
    news: schemas.NewsInfoUpdate,
    db: Session = Depends(get_db)
):
    """
    更新新闻
    """
    db_news = crud.news.update_news(db=db, news_id=news_id, news=news)
    if not db_news:
        raise HTTPException(status_code=404, detail="新闻不存在")
    return db_news

@router.delete("/{news_id}")
def delete_news(news_id: int, db: Session = Depends(get_db)):
    """
    删除新闻
    """
    success = crud.news.delete_news(db=db, news_id=news_id)
    if not success:
        raise HTTPException(status_code=404, detail="新闻不存在")
    return {"message": "删除成功"}
