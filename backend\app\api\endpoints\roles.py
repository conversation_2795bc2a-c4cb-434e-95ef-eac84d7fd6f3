from typing import List
from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session
from app import crud, schemas
from app.database import get_db

router = APIRouter()

# 角色管理相关接口
@router.get("/", response_model=List[schemas.Role])
def get_roles(
    skip: int = 0,
    limit: int = 100,
    db: Session = Depends(get_db)
):
    """
    获取角色列表
    """
    return crud.role.get_roles(db, skip=skip, limit=limit)

@router.get("/{role_id}", response_model=schemas.Role)
def get_role(role_id: int, db: Session = Depends(get_db)):
    """
    获取单个角色详情
    """
    role = crud.role.get_role(db, role_id=role_id)
    if not role:
        raise HTTPException(status_code=404, detail="角色不存在")
    return role

@router.post("/", response_model=schemas.Role)
def create_role(role: schemas.RoleCreate, db: Session = Depends(get_db)):
    """
    创建角色
    """
    # 检查角色编码是否已存在
    existing_role = crud.role.get_role_by_code(db, code=role.code)
    if existing_role:
        raise HTTPException(status_code=400, detail="角色编码已存在")
    
    return crud.role.create_role(db=db, role=role)

@router.put("/{role_id}", response_model=schemas.Role)
def update_role(
    role_id: int,
    role: schemas.RoleUpdate,
    db: Session = Depends(get_db)
):
    """
    更新角色
    """
    # 检查角色是否存在
    existing_role = crud.role.get_role(db, role_id=role_id)
    if not existing_role:
        raise HTTPException(status_code=404, detail="角色不存在")
    
    # 如果更新角色编码，检查是否重复
    if role.role_code and role.role_code != existing_role.role_code:
        code_exists = crud.role.get_role_by_code(db, code=role.role_code)
        if code_exists:
            raise HTTPException(status_code=400, detail="角色编码已存在")
    
    updated_role = crud.role.update_role(db=db, role_id=role_id, role=role)
    if not updated_role:
        raise HTTPException(status_code=404, detail="角色不存在")
    
    return updated_role

@router.delete("/{role_id}")
def delete_role(role_id: int, db: Session = Depends(get_db)):
    """
    删除角色
    """
    # 检查是否有用户使用该角色
    users_with_role = crud.role.get_users_by_role(db, role_id=role_id)
    if users_with_role:
        raise HTTPException(status_code=400, detail="该角色正在被用户使用，无法删除")
    
    success = crud.role.delete_role(db=db, role_id=role_id)
    if not success:
        raise HTTPException(status_code=404, detail="角色不存在")
    return {"message": "删除成功"}

@router.post("/{role_id}/permissions")
def assign_role_permissions(
    role_id: int,
    permission_assign: schemas.RolePermissionAssign,
    db: Session = Depends(get_db)
):
    """
    为角色分配权限
    """
    # 检查角色是否存在
    role = crud.role.get_role(db, role_id=role_id)
    if not role:
        raise HTTPException(status_code=404, detail="角色不存在")
    
    # 检查权限是否都存在
    for permission_id in permission_assign.permission_ids:
        permission = crud.role.get_permission(db, permission_id=permission_id)
        if not permission:
            raise HTTPException(status_code=404, detail=f"权限ID {permission_id} 不存在")
    
    # 分配权限
    permission_assign.role_id = role_id
    success = crud.role.assign_role_permissions(db=db, role_permission_assign=permission_assign)
    if not success:
        raise HTTPException(status_code=500, detail="权限分配失败")
    
    return {"message": "权限分配成功"}

@router.get("/{role_id}/permissions", response_model=List[schemas.Permission])
def get_role_permissions(role_id: int, db: Session = Depends(get_db)):
    """
    获取角色的权限列表
    """
    # 检查角色是否存在
    role = crud.role.get_role(db, role_id=role_id)
    if not role:
        raise HTTPException(status_code=404, detail="角色不存在")
    
    return crud.role.get_role_permissions(db, role_id=role_id)

# 权限管理相关接口
@router.get("/permissions/", response_model=List[schemas.Permission])
def get_permissions(
    skip: int = 0,
    limit: int = 100,
    permission_type: str = None,
    db: Session = Depends(get_db)
):
    """
    获取权限列表
    """
    if permission_type:
        return crud.role.get_permissions_by_type(db, permission_type=permission_type)
    else:
        return crud.role.get_permissions(db, skip=skip, limit=limit)

@router.get("/permissions/{permission_id}", response_model=schemas.Permission)
def get_permission(permission_id: int, db: Session = Depends(get_db)):
    """
    获取单个权限详情
    """
    permission = crud.role.get_permission(db, permission_id=permission_id)
    if not permission:
        raise HTTPException(status_code=404, detail="权限不存在")
    return permission

@router.post("/permissions/", response_model=schemas.Permission)
def create_permission(permission: schemas.PermissionCreate, db: Session = Depends(get_db)):
    """
    创建权限
    """
    # 检查权限编码是否已存在
    existing_permission = crud.role.get_permission_by_code(db, code=permission.code)
    if existing_permission:
        raise HTTPException(status_code=400, detail="权限编码已存在")
    
    return crud.role.create_permission(db=db, permission=permission)

@router.put("/permissions/{permission_id}", response_model=schemas.Permission)
def update_permission(
    permission_id: int,
    permission: schemas.PermissionUpdate,
    db: Session = Depends(get_db)
):
    """
    更新权限
    """
    # 检查权限是否存在
    existing_permission = crud.role.get_permission(db, permission_id=permission_id)
    if not existing_permission:
        raise HTTPException(status_code=404, detail="权限不存在")
    
    # 如果更新权限编码，检查是否重复
    if permission.code and permission.code != existing_permission.code:
        code_exists = crud.role.get_permission_by_code(db, code=permission.code)
        if code_exists:
            raise HTTPException(status_code=400, detail="权限编码已存在")
    
    updated_permission = crud.role.update_permission(db=db, permission_id=permission_id, permission=permission)
    if not updated_permission:
        raise HTTPException(status_code=404, detail="权限不存在")
    
    return updated_permission

@router.delete("/permissions/{permission_id}")
def delete_permission(permission_id: int, db: Session = Depends(get_db)):
    """
    删除权限
    """
    success = crud.role.delete_permission(db=db, permission_id=permission_id)
    if not success:
        raise HTTPException(status_code=404, detail="权限不存在")
    return {"message": "删除成功"}
