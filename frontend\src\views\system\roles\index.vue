<template>
  <div class="role-management">
    <el-card class="box-card">
      <template #header>
        <div class="card-header">
          <span>角色管理</span>
          <div>
            <el-button type="success" @click="handleManagePermissions">
              <el-icon><Setting /></el-icon>
              权限管理
            </el-button>
            <el-button type="primary" @click="handleCreate">
              <el-icon><Plus /></el-icon>
              新建角色
            </el-button>
          </div>
        </div>
      </template>

      <!-- 数据表格 -->
      <el-table :data="tableData" style="width: 100%" v-loading="loading">
        <el-table-column prop="id" label="ID" width="80" />
        <el-table-column prop="name" label="角色名称" min-width="150" />
        <el-table-column prop="code" label="角色编码" min-width="150" />
        <el-table-column prop="description" label="描述" min-width="200" show-overflow-tooltip />
        <el-table-column prop="status" label="状态" width="100">
          <template #default="scope">
            <el-tag :type="scope.row.status === 1 ? 'success' : 'danger'">
              {{ scope.row.status === 1 ? '启用' : '禁用' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="create_time" label="创建时间" width="180">
          <template #default="scope">
            {{ formatDate(scope.row.create_time) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="280" fixed="right">
          <template #default="scope">
            <el-button size="small" @click="handleView(scope.row)">查看</el-button>
            <el-button size="small" type="primary" @click="handleEdit(scope.row)">编辑</el-button>
            <el-button size="small" type="warning" @click="handleAssignPermissions(scope.row)">分配权限</el-button>
            <el-button size="small" type="danger" @click="handleDelete(scope.row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination">
        <el-pagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.size"
          :page-sizes="[10, 20, 50, 100]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 查看详情对话框 -->
    <el-dialog v-model="dialogVisible" title="角色详情" width="60%">
      <div v-if="currentItem">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="角色ID">{{ currentItem.id }}</el-descriptions-item>
          <el-descriptions-item label="角色名称">{{ currentItem.name }}</el-descriptions-item>
          <el-descriptions-item label="角色编码">{{ currentItem.code }}</el-descriptions-item>
          <el-descriptions-item label="状态">
            <el-tag :type="currentItem.status === 1 ? 'success' : 'danger'">
              {{ currentItem.status === 1 ? '启用' : '禁用' }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="创建时间">{{ formatDate(currentItem.create_time) }}</el-descriptions-item>
          <el-descriptions-item label="更新时间">{{ formatDate(currentItem.update_time) }}</el-descriptions-item>
          <el-descriptions-item label="角色描述" :span="2">{{ currentItem.description || '无' }}</el-descriptions-item>
        </el-descriptions>
      </div>
    </el-dialog>

    <!-- 新建/编辑角色对话框 -->
    <el-dialog
      v-model="formDialogVisible"
      :title="isEdit ? '编辑角色' : '新建角色'"
      width="50%"
      :close-on-click-modal="false"
    >
      <el-form
        ref="formRef"
        :model="form"
        :rules="rules"
        label-width="100px"
      >
        <el-form-item label="角色名称" prop="name">
          <el-input v-model="form.name" placeholder="请输入角色名称" />
        </el-form-item>
        <el-form-item label="角色编码" prop="code">
          <el-input v-model="form.code" placeholder="请输入角色编码" />
        </el-form-item>
        <el-form-item label="角色描述" prop="description">
          <el-input
            v-model="form.description"
            type="textarea"
            :rows="3"
            placeholder="请输入角色描述"
          />
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-radio-group v-model="form.status">
            <el-radio :label="1">启用</el-radio>
            <el-radio :label="0">禁用</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="formDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleSubmit" :loading="submitLoading">
            {{ isEdit ? '更新' : '创建' }}
          </el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 分配权限对话框 -->
    <el-dialog
      v-model="permissionDialogVisible"
      title="分配权限"
      width="60%"
    >
      <div v-if="currentRole">
        <p>为角色 <strong>{{ currentRole.name }}</strong> 分配权限：</p>
        <el-tree
          ref="permissionTreeRef"
          :data="permissionTreeData"
          :props="{ children: 'children', label: 'name' }"
          show-checkbox
          node-key="id"
          :default-checked-keys="selectedPermissions"
          style="margin-top: 20px;"
        />
      </div>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="permissionDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handlePermissionSubmit" :loading="permissionSubmitLoading">
            确定
          </el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 权限管理对话框 -->
    <el-dialog
      v-model="permissionManageDialogVisible"
      title="权限管理"
      width="80%"
      :close-on-click-modal="false"
    >
      <div class="permission-manage">
        <div class="permission-header">
          <el-button type="primary" @click="handleCreatePermission">
            <el-icon><Plus /></el-icon>
            新建权限
          </el-button>
        </div>

        <el-table :data="allPermissions" style="width: 100%; margin-top: 20px;" v-loading="permissionLoading">
          <el-table-column prop="id" label="ID" width="80" />
          <el-table-column prop="name" label="权限名称" min-width="150" />
          <el-table-column prop="code" label="权限编码" min-width="150" />
          <el-table-column prop="type" label="类型" width="100">
            <template #default="scope">
              <el-tag :type="scope.row.type === 'menu' ? 'primary' : 'success'">
                {{ scope.row.type === 'menu' ? '菜单' : '按钮' }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="path" label="路径" min-width="150" />
          <el-table-column prop="sort" label="排序" width="80" />
          <el-table-column prop="status" label="状态" width="100">
            <template #default="scope">
              <el-tag :type="scope.row.status === 1 ? 'success' : 'danger'">
                {{ scope.row.status === 1 ? '启用' : '禁用' }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column label="操作" width="150" fixed="right">
            <template #default="scope">
              <el-button size="small" type="primary" @click="handleEditPermission(scope.row)">编辑</el-button>
              <el-button size="small" type="danger" @click="handleDeletePermission(scope.row)">删除</el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </el-dialog>

    <!-- 新建/编辑权限对话框 -->
    <el-dialog
      v-model="permissionFormDialogVisible"
      :title="isEditPermission ? '编辑权限' : '新建权限'"
      width="50%"
      :close-on-click-modal="false"
    >
      <el-form
        ref="permissionFormRef"
        :model="permissionForm"
        :rules="permissionRules"
        label-width="100px"
      >
        <el-form-item label="权限名称" prop="name">
          <el-input v-model="permissionForm.name" placeholder="请输入权限名称" />
        </el-form-item>
        <el-form-item label="权限编码" prop="code">
          <el-input v-model="permissionForm.code" placeholder="请输入权限编码" />
        </el-form-item>
        <el-form-item label="权限类型" prop="type">
          <el-select v-model="permissionForm.type" placeholder="请选择权限类型">
            <el-option label="菜单" value="menu" />
            <el-option label="按钮" value="button" />
          </el-select>
        </el-form-item>
        <el-form-item label="父权限ID" prop="parent_id">
          <el-input-number v-model="permissionForm.parent_id" :min="0" style="width: 100%" />
        </el-form-item>
        <el-form-item label="路径" prop="path">
          <el-input v-model="permissionForm.path" placeholder="请输入路径" />
        </el-form-item>
        <el-form-item label="组件" prop="component">
          <el-input v-model="permissionForm.component" placeholder="请输入组件" />
        </el-form-item>
        <el-form-item label="图标" prop="icon">
          <el-input v-model="permissionForm.icon" placeholder="请输入图标" />
        </el-form-item>
        <el-form-item label="排序" prop="sort">
          <el-input-number v-model="permissionForm.sort" :min="0" style="width: 100%" />
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-radio-group v-model="permissionForm.status">
            <el-radio :label="1">启用</el-radio>
            <el-radio :label="0">禁用</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="permissionFormDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handlePermissionFormSubmit" :loading="permissionFormSubmitLoading">
            {{ isEditPermission ? '更新' : '创建' }}
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus, Setting } from '@element-plus/icons-vue'
import type { FormInstance } from 'element-plus'
import {
  getRoles,
  createRole,
  updateRole,
  deleteRole,
  assignRolePermissions,
  getRolePermissions,
  getPermissions,
  createPermission,
  updatePermission,
  deletePermission
} from '@/api/system'

// 响应式数据
const loading = ref(false)
const submitLoading = ref(false)
const permissionSubmitLoading = ref(false)
const permissionLoading = ref(false)
const permissionFormSubmitLoading = ref(false)
const tableData = ref([])
const dialogVisible = ref(false)
const formDialogVisible = ref(false)
const permissionDialogVisible = ref(false)
const permissionManageDialogVisible = ref(false)
const permissionFormDialogVisible = ref(false)
const isEdit = ref(false)
const isEditPermission = ref(false)
const currentItem = ref(null)
const currentRole = ref(null)
const currentId = ref<number | null>(null)
const currentPermissionId = ref<number | null>(null)
const formRef = ref<FormInstance>()
const permissionFormRef = ref<FormInstance>()
const permissionTreeRef = ref()
const allPermissions = ref([])
const permissionTreeData = ref([])
const selectedPermissions = ref([])

// 角色表单
const form = reactive({
  name: '',
  code: '',
  description: '',
  status: 1
})

// 权限表单
const permissionForm = reactive({
  name: '',
  code: '',
  type: 'menu',
  parent_id: 0,
  path: '',
  component: '',
  icon: '',
  sort: 0,
  status: 1
})

// 分页
const pagination = reactive({
  page: 1,
  size: 10,
  total: 0
})

// 表单验证规则
const rules = {
  name: [
    { required: true, message: '请输入角色名称', trigger: 'blur' },
    { min: 2, max: 50, message: '角色名称长度在 2 到 50 个字符', trigger: 'blur' }
  ],
  code: [
    { required: true, message: '请输入角色编码', trigger: 'blur' },
    { min: 2, max: 50, message: '角色编码长度在 2 到 50 个字符', trigger: 'blur' }
  ]
}

const permissionRules = {
  name: [
    { required: true, message: '请输入权限名称', trigger: 'blur' }
  ],
  code: [
    { required: true, message: '请输入权限编码', trigger: 'blur' }
  ],
  type: [
    { required: true, message: '请选择权限类型', trigger: 'change' }
  ]
}

// 工具方法
const formatDate = (dateString: string) => {
  if (!dateString) return '-'
  return new Date(dateString).toLocaleString()
}

// 构建权限树
const buildPermissionTree = (permissions: any[]) => {
  const tree = []
  const map = new Map()

  // 先创建所有节点的映射
  permissions.forEach(permission => {
    map.set(permission.id, { ...permission, children: [] })
  })

  // 构建树结构
  permissions.forEach(permission => {
    const node = map.get(permission.id)
    if (permission.parent_id === 0) {
      tree.push(node)
    } else {
      const parent = map.get(permission.parent_id)
      if (parent) {
        parent.children.push(node)
      }
    }
  })

  return tree
}

// 获取数据
const fetchData = async () => {
  loading.value = true
  try {
    const params = {
      skip: (pagination.page - 1) * pagination.size,
      limit: pagination.size
    }
    const response = await getRoles(params)
    tableData.value = response || []
    pagination.total = tableData.value.length
  } catch (error) {
    ElMessage.error('获取数据失败')
    console.error(error)
  } finally {
    loading.value = false
  }
}

// 获取所有权限
const fetchPermissions = async () => {
  try {
    const response = await getPermissions({ limit: 1000 })
    allPermissions.value = response || []
    permissionTreeData.value = buildPermissionTree(response || [])
  } catch (error) {
    console.error('获取权限列表失败:', error)
  }
}

// 事件处理
const handleCreate = () => {
  isEdit.value = false
  currentId.value = null
  resetForm()
  formDialogVisible.value = true
}

const handleView = (row: any) => {
  currentItem.value = row
  dialogVisible.value = true
}

const handleEdit = (row: any) => {
  isEdit.value = true
  currentId.value = row.id

  // 填充表单数据
  form.name = row.name
  form.code = row.code
  form.description = row.description
  form.status = row.status

  formDialogVisible.value = true
}

const handleDelete = async (row: any) => {
  try {
    await ElMessageBox.confirm('确定要删除这个角色吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })

    await deleteRole(row.id)
    ElMessage.success('删除成功')
    fetchData()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败')
    }
  }
}

const handleAssignPermissions = async (row: any) => {
  currentRole.value = row

  // 获取角色当前权限
  try {
    const rolePermissions = await getRolePermissions(row.id)
    selectedPermissions.value = rolePermissions.map((permission: any) => permission.id)
  } catch (error) {
    selectedPermissions.value = []
  }

  permissionDialogVisible.value = true
}

const handleManagePermissions = () => {
  permissionManageDialogVisible.value = true
  fetchPermissions()
}

const resetForm = () => {
  form.name = ''
  form.code = ''
  form.description = ''
  form.status = 1
}

const resetPermissionForm = () => {
  permissionForm.name = ''
  permissionForm.code = ''
  permissionForm.type = 'menu'
  permissionForm.parent_id = 0
  permissionForm.path = ''
  permissionForm.component = ''
  permissionForm.icon = ''
  permissionForm.sort = 0
  permissionForm.status = 1
}

const handleSubmit = async () => {
  if (!formRef.value) return

  try {
    await formRef.value.validate()
    submitLoading.value = true

    if (isEdit.value && currentId.value) {
      await updateRole(currentId.value, form)
      ElMessage.success('更新成功')
    } else {
      await createRole(form)
      ElMessage.success('创建成功')
    }

    formDialogVisible.value = false
    fetchData()
  } catch (error) {
    if (error !== false) {
      ElMessage.error(isEdit.value ? '更新失败' : '创建失败')
      console.error(error)
    }
  } finally {
    submitLoading.value = false
  }
}

const handlePermissionSubmit = async () => {
  if (!currentRole.value || !permissionTreeRef.value) return

  try {
    permissionSubmitLoading.value = true
    const checkedKeys = permissionTreeRef.value.getCheckedKeys()
    const halfCheckedKeys = permissionTreeRef.value.getHalfCheckedKeys()
    const allCheckedKeys = [...checkedKeys, ...halfCheckedKeys]

    await assignRolePermissions(currentRole.value.id, allCheckedKeys)
    ElMessage.success('权限分配成功')
    permissionDialogVisible.value = false
  } catch (error) {
    ElMessage.error('权限分配失败')
    console.error(error)
  } finally {
    permissionSubmitLoading.value = false
  }
}

// 权限管理相关方法
const handleCreatePermission = () => {
  isEditPermission.value = false
  currentPermissionId.value = null
  resetPermissionForm()
  permissionFormDialogVisible.value = true
}

const handleEditPermission = (row: any) => {
  isEditPermission.value = true
  currentPermissionId.value = row.id

  // 填充表单数据
  permissionForm.name = row.name
  permissionForm.code = row.code
  permissionForm.type = row.type
  permissionForm.parent_id = row.parent_id
  permissionForm.path = row.path
  permissionForm.component = row.component
  permissionForm.icon = row.icon
  permissionForm.sort = row.sort
  permissionForm.status = row.status

  permissionFormDialogVisible.value = true
}

const handleDeletePermission = async (row: any) => {
  try {
    await ElMessageBox.confirm('确定要删除这个权限吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })

    await deletePermission(row.id)
    ElMessage.success('删除成功')
    fetchPermissions()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败')
    }
  }
}

const handlePermissionFormSubmit = async () => {
  if (!permissionFormRef.value) return

  try {
    await permissionFormRef.value.validate()
    permissionFormSubmitLoading.value = true

    if (isEditPermission.value && currentPermissionId.value) {
      await updatePermission(currentPermissionId.value, permissionForm)
      ElMessage.success('更新成功')
    } else {
      await createPermission(permissionForm)
      ElMessage.success('创建成功')
    }

    permissionFormDialogVisible.value = false
    fetchPermissions()
  } catch (error) {
    if (error !== false) {
      ElMessage.error(isEditPermission.value ? '更新失败' : '创建失败')
      console.error(error)
    }
  } finally {
    permissionFormSubmitLoading.value = false
  }
}

const handleSizeChange = (size: number) => {
  pagination.size = size
  pagination.page = 1
  fetchData()
}

const handleCurrentChange = (page: number) => {
  pagination.page = page
  fetchData()
}

// 初始化
onMounted(() => {
  fetchData()
  fetchPermissions()
})
</script>

<style scoped>
.role-management {
  padding: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.pagination {
  margin-top: 20px;
  text-align: right;
}

.permission-manage {
  min-height: 400px;
}

.permission-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}
</style>