#!/usr/bin/env python3
"""
数据库连接测试脚本
"""

def test_imports():
    print("测试导入...")
    try:
        from app.database import SessionLocal, engine, Base
        print("✓ 数据库模块导入成功")
        
        from app.models import SysAdmin
        print("✓ 模型导入成功")
        
        from app.core.security import get_password_hash
        print("✓ 安全模块导入成功")
        
        return True
    except Exception as e:
        print(f"✗ 导入失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_config():
    print("\n测试配置...")
    try:
        from app.core.config import settings
        print(f"✓ 项目名称: {settings.PROJECT_NAME}")
        print(f"✓ API版本: {settings.API_V1_STR}")
        print(f"✓ 数据库URL: {settings.DATABASE_URL}")
        return True
    except Exception as e:
        print(f"✗ 配置加载失败: {e}")
        return False

def test_db_connection():
    print("\n测试数据库连接...")
    try:
        from app.database import engine
        connection = engine.connect()
        connection.close()
        print("✓ 数据库连接成功")
        return True
    except Exception as e:
        print(f"✗ 数据库连接失败: {e}")
        print("请检查:")
        print("1. MySQL服务是否正在运行")
        print("2. 数据库 'lun' 是否已创建")
        print("3. .env 文件中的数据库配置是否正确")
        return False

if __name__ == "__main__":
    print("=== 数据库连接诊断 ===")
    
    if not test_imports():
        exit(1)
    
    if not test_config():
        exit(1)
    
    if not test_db_connection():
        exit(1)
    
    print("\n✓ 所有测试通过！可以运行 init_admin.py")
