from typing import List, Optional
from sqlalchemy.orm import Session
from app.models.news import NewsInfo
from app.schemas.news import NewsInfoCreate, NewsInfoUpdate

# 新闻CRUD操作
def get_news(db: Session, news_id: int) -> Optional[NewsInfo]:
    return db.query(NewsInfo).filter(NewsInfo.id == news_id, NewsInfo.deleted == 0).first()

def get_news_list(db: Session, skip: int = 0, limit: int = 100) -> List[NewsInfo]:
    return db.query(NewsInfo).filter(NewsInfo.deleted == 0).offset(skip).limit(limit).all()

def get_news_by_category(db: Session, category: str, skip: int = 0, limit: int = 100) -> List[NewsInfo]:
    return db.query(NewsInfo).filter(NewsInfo.category == category, NewsInfo.deleted == 0).offset(skip).limit(limit).all()

def create_news(db: Session, news: NewsInfoCreate) -> NewsInfo:
    db_news = NewsInfo(**news.dict())
    db.add(db_news)
    db.commit()
    db.refresh(db_news)
    return db_news

def update_news(db: Session, news_id: int, news: NewsInfoUpdate) -> Optional[NewsInfo]:
    db_news = get_news(db, news_id)
    if db_news:
        update_data = news.dict(exclude_unset=True)
        for field, value in update_data.items():
            setattr(db_news, field, value)
        db.commit()
        db.refresh(db_news)
    return db_news

def delete_news(db: Session, news_id: int) -> bool:
    db_news = get_news(db, news_id)
    if db_news:
        db_news.deleted = 1
        db.commit()
        return True
    return False

def increment_read_count(db: Session, news_id: int) -> Optional[NewsInfo]:
    db_news = get_news(db, news_id)
    if db_news:
        current_count = int(db_news.read_count) if db_news.read_count.isdigit() else 0
        db_news.read_count = str(current_count + 1)
        db.commit()
        db.refresh(db_news)
    return db_news
