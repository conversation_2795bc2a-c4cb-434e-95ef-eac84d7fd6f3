/*
 Navicat Premium Dump SQL

 Source Server         : 本地
 Source Server Type    : MySQL
 Source Server Version : 80018 (8.0.18)
 Source Host           : localhost:3306
 Source Schema         : lun

 Target Server Type    : MySQL
 Target Server Version : 80018 (8.0.18)
 File Encoding         : 65001

 Date: 06/06/2025 22:16:54
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for activity
-- ----------------------------
DROP TABLE IF EXISTS `activity`;
CREATE TABLE `activity` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '活动ID',
  `category_id` bigint(20) NOT NULL COMMENT '分类ID',
  `title` varchar(255) NOT NULL COMMENT '活动标题',
  `summary` varchar(500) DEFAULT NULL COMMENT '活动摘要',
  `content` text NOT NULL COMMENT '活动内容',
  `cover_image` varchar(255) DEFAULT NULL COMMENT '封面图片',
  `address` varchar(255) DEFAULT NULL COMMENT '活动地点',
  `start_time` datetime NOT NULL COMMENT '活动开始时间',
  `end_time` datetime NOT NULL COMMENT '活动结束时间',
  `signup_start_time` datetime DEFAULT NULL COMMENT '报名开始时间',
  `signup_end_time` datetime DEFAULT NULL COMMENT '报名结束时间',
  `max_participants` int(11) DEFAULT NULL COMMENT '最大参与人数',
  `current_participants` int(11) DEFAULT '0' COMMENT '当前参与人数',
  `contact_person` varchar(50) DEFAULT NULL COMMENT '联系人',
  `contact_phone` varchar(20) DEFAULT NULL COMMENT '联系电话',
  `status` tinyint(1) NOT NULL DEFAULT '0' COMMENT '状态：0-未审核，1-未开始，2-进行中，3-已结束，4-已取消',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否删除：0-未删除，1-已删除',
  PRIMARY KEY (`id`),
  KEY `idx_category_id` (`category_id`),
  KEY `idx_start_time` (`start_time`),
  KEY `idx_end_time` (`end_time`),
  KEY `idx_signup_end_time` (`signup_end_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='公益活动表';

-- ----------------------------
-- Records of activity
-- ----------------------------
BEGIN;
COMMIT;

-- ----------------------------
-- Table structure for activity_category
-- ----------------------------
DROP TABLE IF EXISTS `activity_category`;
CREATE TABLE `activity_category` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '分类ID',
  `name` varchar(50) NOT NULL COMMENT '分类名称',
  `description` varchar(255) DEFAULT NULL COMMENT '分类描述',
  `sort` int(11) DEFAULT '0' COMMENT '排序',
  `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态：0-禁用，1-启用',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否删除：0-未删除，1-已删除',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='公益活动分类表';

-- ----------------------------
-- Records of activity_category
-- ----------------------------
BEGIN;
COMMIT;

-- ----------------------------
-- Table structure for activity_participant
-- ----------------------------
DROP TABLE IF EXISTS `activity_participant`;
CREATE TABLE `activity_participant` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '记录ID',
  `activity_id` bigint(20) NOT NULL COMMENT '活动ID',
  `user_id` bigint(20) NOT NULL COMMENT '用户ID',
  `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态：0-已取消，1-已报名，2-已签到，3-已完成',
  `signup_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '报名时间',
  `checkin_time` datetime DEFAULT NULL COMMENT '签到时间',
  `complete_time` datetime DEFAULT NULL COMMENT '完成时间',
  `remark` varchar(255) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_activity_user` (`activity_id`,`user_id`),
  KEY `idx_activity_id` (`activity_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_signup_time` (`signup_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='活动参与记录表';

-- ----------------------------
-- Records of activity_participant
-- ----------------------------
BEGIN;
COMMIT;

-- ----------------------------
-- Table structure for category
-- ----------------------------
DROP TABLE IF EXISTS `category`;
CREATE TABLE `category` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `name` varchar(64) NOT NULL COMMENT '分类名称',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `deleted` tinyint(1) DEFAULT '0' COMMENT '是否删除',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=6 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='题目分类表';

-- ----------------------------
-- Records of category
-- ----------------------------
BEGIN;
INSERT INTO `category` (`id`, `name`, `create_time`, `update_time`, `deleted`) VALUES (1, '基础知识', '2025-03-17 13:59:30', '2025-03-17 13:59:30', 0);
INSERT INTO `category` (`id`, `name`, `create_time`, `update_time`, `deleted`) VALUES (2, '法律法规', '2025-03-17 13:59:30', '2025-03-17 13:59:30', 0);
INSERT INTO `category` (`id`, `name`, `create_time`, `update_time`, `deleted`) VALUES (3, '安全常识', '2025-03-17 13:59:30', '2025-03-17 13:59:30', 0);
INSERT INTO `category` (`id`, `name`, `create_time`, `update_time`, `deleted`) VALUES (4, '环保知识', '2025-03-17 13:59:30', '2025-03-17 13:59:30', 0);
INSERT INTO `category` (`id`, `name`, `create_time`, `update_time`, `deleted`) VALUES (5, '文化素养', '2025-03-17 13:59:30', '2025-03-17 13:59:30', 0);
COMMIT;

-- ----------------------------
-- Table structure for charity
-- ----------------------------
DROP TABLE IF EXISTS `charity`;
CREATE TABLE `charity` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `title` varchar(255) NOT NULL COMMENT '标题',
  `description` varchar(500) DEFAULT NULL COMMENT '描述',
  `content` text COMMENT '内容',
  `time` varchar(64) DEFAULT NULL COMMENT '活动时间',
  `location` varchar(255) DEFAULT NULL COMMENT '活动地点',
  `participants` int(11) DEFAULT '0' COMMENT '当前参与人数',
  `max_participants` int(11) DEFAULT '0' COMMENT '最大参与人数',
  `image` varchar(255) DEFAULT NULL COMMENT '图片',
  `status` varchar(20) DEFAULT 'upcoming' COMMENT '状态（ongoing-进行中，upcoming-即将开始，finished-已结束）',
  `category` varchar(50) DEFAULT '环保' COMMENT '活动分类',
  `organizer` varchar(64) DEFAULT NULL COMMENT '组织者',
  `contact` varchar(64) DEFAULT NULL COMMENT '联系方式',
  `requirements` text COMMENT '参与要求',
  `notices` text COMMENT '活动须知',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `deleted` tinyint(1) DEFAULT '0' COMMENT '是否删除',
  PRIMARY KEY (`id`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB AUTO_INCREMENT=6 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='公益活动表';

-- ----------------------------
-- Records of charity
-- ----------------------------
BEGIN;
INSERT INTO `charity` (`id`, `title`, `description`, `content`, `time`, `location`, `participants`, `max_participants`, `image`, `status`, `category`, `organizer`, `contact`, `requirements`, `notices`, `create_time`, `update_time`, `deleted`) VALUES (1, '植树护绿 建美丽家园', '春季植树活动，为城市增添绿色', '<p>为响应国家绿化号召，改善城市生态环境，我们将组织一次大规模的植树活动。</p><p>活动中，志愿者将在专业人员的指导下，学习正确的植树技术，并亲手种下树苗，为城市增添一抹绿色。</p><p>活动结束后，我们将对植树区域进行定期维护，确保树苗成活。</p>', '2023-03-12 09:00-12:00', '城市森林公园东区', 120, 200, 'https://ai-public.mastergo.com/ai/img_res/c88b5cb0f273a013c0a6ff4bf9f39892.jpg', 'upcoming', '1', '绿色未来环保协会', '13900139000', '年满18周岁，身体健康，能够进行轻体力劳动', '<p>1. 请穿着舒适的衣物和运动鞋，自备帽子、手套和水壶。</p><p>2. 活动当天将提供植树工具和树苗，请按照指导人员的指示进行操作。</p><p>3. 活动地点可能没有遮阳设施，请做好防晒准备。</p><p>4. 活动结束后请将垃圾带走，保持环境整洁。</p>', '2025-03-17 13:59:30', '2025-03-17 13:59:30', 0);
INSERT INTO `charity` (`id`, `title`, `description`, `content`, `time`, `location`, `participants`, `max_participants`, `image`, `status`, `category`, `organizer`, `contact`, `requirements`, `notices`, `create_time`, `update_time`, `deleted`) VALUES (2, '关爱老人 情暖夕阳1', '走进养老院，为老人送去温暖和关怀', '<p>老吾老以及人之老。本次活动将组织志愿者走进市第三养老院，为老人们送去节日的祝福和关怀。</p><p>活动内容包括文艺表演、健康讲座、一对一陪伴交流等，让老人们感受到社会的温暖。</p><p>欢迎有才艺特长的志愿者报名参加，共同为老人们带去欢乐。</p>', '2023-01-15 14:00-16:30', '市第三养老院', 35, 50, 'https://ai-public.mastergo.com/ai/img_res/c88b5cb0f273a013c0a6ff4bf9f39892.jpg', 'upcoming', '2', '阳光志愿者协会', '13900139001', '有爱心，有耐心，尊重老人，有才艺特长者优先', '<p>1. 请穿着舒适的衣物和运动鞋，自备帽子、手套和水壶。</p><p>2. 活动当天将提供植树工具和树苗，请按照指导人员的指示进行操作。</p><p>3. 活动地点可能没有遮阳设施，请做好防晒准备。</p><p>4. 活动结束后请将垃圾带走，保持环境整洁。</p>', '2025-03-17 13:59:30', '2025-03-17 13:59:30', 0);
INSERT INTO `charity` (`id`, `title`, `description`, `content`, `time`, `location`, `participants`, `max_participants`, `image`, `status`, `category`, `organizer`, `contact`, `requirements`, `notices`, `create_time`, `update_time`, `deleted`) VALUES (3, '爱心助学 点亮梦想', '为山区儿童捐赠学习用品和图书', '<p>教育是改变命运的力量。本次活动旨在为山区学校的孩子们捐赠学习用品和图书，帮助他们获得更好的学习条件。</p><p>我们将收集爱心人士捐赠的文具、书包、图书等物资，并组织志愿者亲自送到山区学校，与孩子们进行互动交流。</p>', '2023-04-22 全天', '希望小学（山区）', 25, 30, 'https://ai-public.mastergo.com/ai/img_res/c88b5cb0f273a013c0a6ff4bf9f39892.jpg', 'upcoming', '3', '希望工程志愿者联盟', '13900139002', '有教育相关经验者优先，需自行解决往返交通', '<p>1. 捐赠物资请确保干净整洁，书籍内容健康积极。</p><p>2. 山区道路可能崎岖，请穿着舒适的鞋子，准备必要的药品。</p><p>3. 与孩子互动时请尊重当地风俗习惯，不要随意拍照发布。</p><p>4. 活动期间请服从领队安排，确保人身安全。</p>', '2025-03-17 13:59:30', '2025-03-17 13:59:30', 0);
INSERT INTO `charity` (`id`, `title`, `description`, `content`, `time`, `location`, `participants`, `max_participants`, `image`, `status`, `category`, `organizer`, `contact`, `requirements`, `notices`, `create_time`, `update_time`, `deleted`) VALUES (4, '保护海洋 清洁海滩', '海滩垃圾清理活动，守护蓝色家园', '<p>海洋是地球生命的摇篮，保护海洋环境是我们共同的责任。本次活动将组织志愿者清理海滩垃圾，减少海洋污染。</p><p>活动中，志愿者将学习海洋环保知识，了解垃圾分类和回收的重要性，并参与海滩清理工作。</p>', '2023-06-08 08:30-11:30', '银沙湾海滩', 78, 100, 'https://ai-public.mastergo.com/ai/img_res/ea43d8d933d3ac72acbdc42b048eea78.jpg', 'upcoming', '4', '蓝色海洋保护协会', '13900139003', '年满16周岁，身体健康，自备防晒用品', '<p>1. 请穿着轻便服装和防滑鞋，自备防晒霜、墨镜和帽子。</p><p>2. 活动将提供手套、垃圾袋和夹子等工具，请爱护使用。</p><p>3. 清理垃圾时注意安全，不要触碰尖锐物品和不明液体。</p><p>4. 活动结束后请在指定地点洗手消毒。</p>', '2025-03-17 13:59:30', '2025-03-17 13:59:30', 0);
INSERT INTO `charity` (`id`, `title`, `description`, `content`, `time`, `location`, `participants`, `max_participants`, `image`, `status`, `category`, `organizer`, `contact`, `requirements`, `notices`, `create_time`, `update_time`, `deleted`) VALUES (5, '无偿献血 传递生命', '城市中心献血活动，挽救生命', '<p>血液是生命之源，无偿献血是传递爱心的方式。本次活动将在城市中心设立临时献血点，呼吁市民参与无偿献血。</p><p>活动现场将有专业医护人员进行健康咨询和献血指导，确保献血安全。</p><p>每位成功献血的志愿者将获得一份纪念品和健康检查报告。</p>', '2023-02-14 09:00-17:00', '中央广场', 56, 200, 'https://ai-public.mastergo.com/ai/img_res/c88b5cb0f273a013c0a6ff4bf9f39892.jpg', 'upcoming', '5', '红十字会', '13900139004', '年满18周岁，体重男性≥50kg，女性≥45kg，身体健康', '<p>1. 献血前一天请保证充足睡眠，避免剧烈运动。</p><p>2. 献血当天请吃清淡早餐，不要空腹，避免高脂肪食物。</p><p>3. 献血后请在休息区停留至少15分钟，多喝水，避免剧烈运动。</p><p>4. 献血后48小时内避免剧烈运动和重体力劳动，保持伤口干燥。</p>', '2025-03-17 13:59:30', '2025-03-17 13:59:30', 0);
COMMIT;

-- ----------------------------
-- Table structure for charity_category
-- ----------------------------
DROP TABLE IF EXISTS `charity_category`;
CREATE TABLE `charity_category` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `name` varchar(64) NOT NULL COMMENT '分类名称',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `deleted` tinyint(1) DEFAULT '0' COMMENT '是否删除',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=11 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='公益活动分类表';

-- ----------------------------
-- Records of charity_category
-- ----------------------------
BEGIN;
INSERT INTO `charity_category` (`id`, `name`, `create_time`, `update_time`, `deleted`) VALUES (1, '环保公益', '2025-03-17 18:19:56', '2025-03-17 18:19:56', 0);
INSERT INTO `charity_category` (`id`, `name`, `create_time`, `update_time`, `deleted`) VALUES (2, '教育助学', '2025-03-17 18:19:56', '2025-03-17 18:19:56', 0);
INSERT INTO `charity_category` (`id`, `name`, `create_time`, `update_time`, `deleted`) VALUES (3, '扶贫济困', '2025-03-17 18:19:56', '2025-03-17 18:19:56', 0);
INSERT INTO `charity_category` (`id`, `name`, `create_time`, `update_time`, `deleted`) VALUES (4, '关爱老人', '2025-03-17 18:19:56', '2025-03-17 18:19:56', 0);
INSERT INTO `charity_category` (`id`, `name`, `create_time`, `update_time`, `deleted`) VALUES (5, '儿童关怀', '2025-03-17 18:19:56', '2025-03-17 18:19:56', 0);
INSERT INTO `charity_category` (`id`, `name`, `create_time`, `update_time`, `deleted`) VALUES (6, '灾害救助', '2025-03-17 18:19:56', '2025-03-17 18:19:56', 0);
INSERT INTO `charity_category` (`id`, `name`, `create_time`, `update_time`, `deleted`) VALUES (7, '医疗救助', '2025-03-17 18:19:56', '2025-03-17 18:19:56', 0);
INSERT INTO `charity_category` (`id`, `name`, `create_time`, `update_time`, `deleted`) VALUES (8, '动物保护', '2025-03-17 18:19:56', '2025-03-17 18:19:56', 0);
INSERT INTO `charity_category` (`id`, `name`, `create_time`, `update_time`, `deleted`) VALUES (9, '志愿服务', '2025-03-17 18:19:56', '2025-03-17 18:19:56', 0);
INSERT INTO `charity_category` (`id`, `name`, `create_time`, `update_time`, `deleted`) VALUES (10, '社区建设', '2025-03-17 18:19:56', '2025-03-17 18:19:56', 0);
COMMIT;

-- ----------------------------
-- Table structure for favorite
-- ----------------------------
DROP TABLE IF EXISTS `favorite`;
CREATE TABLE `favorite` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `open_id` varchar(64) NOT NULL COMMENT '用户OpenID',
  `question_id` bigint(20) NOT NULL COMMENT '题目ID',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `deleted` tinyint(1) DEFAULT '0' COMMENT '是否删除',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_user_question` (`open_id`,`question_id`),
  KEY `idx_open_id` (`open_id`),
  KEY `idx_question_id` (`question_id`)
) ENGINE=InnoDB AUTO_INCREMENT=6 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='收藏题目表';

-- ----------------------------
-- Records of favorite
-- ----------------------------
BEGIN;
INSERT INTO `favorite` (`id`, `open_id`, `question_id`, `create_time`, `update_time`, `deleted`) VALUES (1, 'oXy1Z5Pu7QgLJqvL1234abcd', 1, '2025-03-17 13:59:30', '2025-03-17 13:59:30', 0);
INSERT INTO `favorite` (`id`, `open_id`, `question_id`, `create_time`, `update_time`, `deleted`) VALUES (2, 'oXy1Z5Pu7QgLJqvL1234abcd', 3, '2025-03-17 13:59:30', '2025-03-17 13:59:30', 0);
INSERT INTO `favorite` (`id`, `open_id`, `question_id`, `create_time`, `update_time`, `deleted`) VALUES (3, 'oXy1Z5Pu7QgLJqvL5678efgh', 2, '2025-03-17 13:59:30', '2025-03-17 13:59:30', 0);
INSERT INTO `favorite` (`id`, `open_id`, `question_id`, `create_time`, `update_time`, `deleted`) VALUES (4, 'oXy1Z5Pu7QgLJqvL5678efgh', 5, '2025-03-17 13:59:30', '2025-03-17 13:59:30', 0);
INSERT INTO `favorite` (`id`, `open_id`, `question_id`, `create_time`, `update_time`, `deleted`) VALUES (5, 'oXy1Z5Pu7QgLJqvL9012ijkl', 4, '2025-03-17 13:59:30', '2025-03-17 13:59:30', 0);
COMMIT;

-- ----------------------------
-- Table structure for mp_user
-- ----------------------------
DROP TABLE IF EXISTS `mp_user`;
CREATE TABLE `mp_user` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '用户ID',
  `openid` varchar(50) NOT NULL COMMENT '微信openid',
  `unionid` varchar(50) DEFAULT NULL COMMENT '微信unionid',
  `nickname` varchar(50) DEFAULT NULL COMMENT '昵称',
  `avatar` varchar(255) DEFAULT NULL COMMENT '头像',
  `gender` tinyint(1) DEFAULT '0' COMMENT '性别：0-未知，1-男，2-女',
  `phone` varchar(20) DEFAULT NULL COMMENT '手机号',
  `province` varchar(50) DEFAULT NULL COMMENT '省份',
  `city` varchar(50) DEFAULT NULL COMMENT '城市',
  `login_times` int(11) DEFAULT '0' COMMENT '登录次数',
  `last_login_time` datetime DEFAULT NULL COMMENT '最后登录时间',
  `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态：0-禁用，1-启用',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否删除：0-未删除，1-已删除',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_openid` (`openid`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='小程序用户表';

-- ----------------------------
-- Records of mp_user
-- ----------------------------
BEGIN;
COMMIT;

-- ----------------------------
-- Table structure for news
-- ----------------------------
DROP TABLE IF EXISTS `news`;
CREATE TABLE `news` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '新闻ID',
  `category_id` bigint(20) NOT NULL COMMENT '类别ID',
  `title` varchar(255) NOT NULL COMMENT '新闻标题',
  `summary` varchar(500) DEFAULT NULL COMMENT '新闻摘要',
  `content` text NOT NULL COMMENT '新闻内容',
  `cover_image` varchar(255) DEFAULT NULL COMMENT '封面图片',
  `author` varchar(50) DEFAULT NULL COMMENT '作者',
  `view_count` int(11) DEFAULT '0' COMMENT '浏览量',
  `is_top` tinyint(1) DEFAULT '0' COMMENT '是否置顶：0-否，1-是',
  `is_recommend` tinyint(1) DEFAULT '0' COMMENT '是否推荐：0-否，1-是',
  `status` tinyint(1) NOT NULL DEFAULT '0' COMMENT '状态：0-草稿，1-已发布，2-已下线',
  `publish_time` datetime DEFAULT NULL COMMENT '发布时间',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否删除：0-未删除，1-已删除',
  PRIMARY KEY (`id`),
  KEY `idx_category_id` (`category_id`),
  KEY `idx_publish_time` (`publish_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='新闻表';

-- ----------------------------
-- Records of news
-- ----------------------------
BEGIN;
COMMIT;

-- ----------------------------
-- Table structure for news_category
-- ----------------------------
DROP TABLE IF EXISTS `news_category`;
CREATE TABLE `news_category` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '类别ID',
  `name` varchar(50) NOT NULL COMMENT '类别名称',
  `description` varchar(255) DEFAULT NULL COMMENT '类别描述',
  `sort` int(11) DEFAULT '0' COMMENT '排序',
  `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态：0-禁用，1-启用',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否删除：0-未删除，1-已删除',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='新闻类别表';

-- ----------------------------
-- Records of news_category
-- ----------------------------
BEGIN;
COMMIT;

-- ----------------------------
-- Table structure for news_info
-- ----------------------------
DROP TABLE IF EXISTS `news_info`;
CREATE TABLE `news_info` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `title` varchar(255) NOT NULL COMMENT '标题',
  `description` varchar(500) DEFAULT NULL COMMENT '描述',
  `content` text COMMENT '内容',
  `read_count` varchar(20) DEFAULT '0' COMMENT '阅读量',
  `comments` varchar(20) DEFAULT '0' COMMENT '评论数',
  `publish_date` varchar(20) DEFAULT NULL COMMENT '发布日期',
  `author` varchar(64) DEFAULT NULL COMMENT '作者',
  `category` varchar(64) DEFAULT NULL COMMENT '分类',
  `image` varchar(255) DEFAULT NULL COMMENT '图片',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `deleted` tinyint(1) DEFAULT '0' COMMENT '是否删除',
  PRIMARY KEY (`id`),
  KEY `idx_category` (`category`)
) ENGINE=InnoDB AUTO_INCREMENT=6 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='新闻资讯表';

-- ----------------------------
-- Records of news_info
-- ----------------------------
BEGIN;
INSERT INTO `news_info` (`id`, `title`, `description`, `content`, `read_count`, `comments`, `publish_date`, `author`, `category`, `image`, `create_time`, `update_time`, `deleted`) VALUES (1, '全国垃圾分类新规将于明年实施', '新规要求全面推行垃圾分类制度，提高资源利用率', '<p>近日，国家发改委等部门联合发布《关于全面推进垃圾分类工作的通知》，要求自2023年1月1日起，全国城市全面推行垃圾分类制度。</p><p>新规明确了垃圾分为可回收物、有害垃圾、厨余垃圾和其他垃圾四类，并对各类垃圾的处理方式进行了详细规定。</p><p>专家表示，垃圾分类对提高资源利用率、改善环境质量具有重要意义。</p>', '1256', '89', '2022-12-15', '环保时报', '1', 'https://ai-public.mastergo.com/ai/img_res/c88b5cb0f273a013c0a6ff4bf9f39892.jpg', '2025-03-17 13:59:30', '2025-03-17 13:59:30', 0);
INSERT INTO `news_info` (`id`, `title`, `description`, `content`, `read_count`, `comments`, `publish_date`, `author`, `category`, `image`, `create_time`, `update_time`, `deleted`) VALUES (2, '我国科学家在量子计算领域取得重大突破', '实现了76个光子的量子计算原型机', '<p>中国科学技术大学潘建伟院士团队近日宣布，成功构建76个光子的量子计算原型机\"九章二号\"，计算能力较此前提升100倍。</p><p>这一突破使我国在量子计算领域继续保持国际领先地位，为未来实现实用化量子计算机奠定了重要基础。</p>', '2345', '156', '2022-11-20', '科技日报', '2', 'https://ai-public.mastergo.com/ai/img_res/ea43d8d933d3ac72acbdc42b048eea78.jpg', '2025-03-17 13:59:30', '2025-03-17 13:59:30', 0);
INSERT INTO `news_info` (`id`, `title`, `description`, `content`, `read_count`, `comments`, `publish_date`, `author`, `category`, `image`, `create_time`, `update_time`, `deleted`) VALUES (3, '最高法发布新司法解释保护未成年人权益', '加强对未成年人的司法保护', '<p>最高人民法院今日发布《关于审理侵害未成年人权益民事案件适用法律若干问题的解释》，进一步明确了监护人责任、学校安全保障义务等内容。</p><p>新司法解释将于2023年1月1日起施行，对于加强未成年人权益保护，预防和减少侵害未成年人权益行为具有重要意义。</p>', '1876', '103', '2022-12-01', '法制日报', '3', 'https://ai-public.mastergo.com/ai/img_res/ea43d8d933d3ac72acbdc42b048eea78.jpg', '2025-03-17 13:59:30', '2025-03-17 13:59:30', 0);
INSERT INTO `news_info` (`id`, `title`, `description`, `content`, `read_count`, `comments`, `publish_date`, `author`, `category`, `image`, `create_time`, `update_time`, `deleted`) VALUES (4, '全国安全生产月活动启动', '今年主题为\"遵守安全生产法，当好第一责任人\"', '<p>6月1日，2022年全国\"安全生产月\"活动正式启动，今年活动的主题是\"遵守安全生产法，当好第一责任人\"。</p><p>活动期间，各地区、各有关部门和单位将组织开展安全宣传咨询日、安全生产公开课、应急演练观摩等活动，提高全民安全意识和应急处置能力。</p>', '1432', '76', '2022-06-01', '安全生产报', '4', 'https://ai-public.mastergo.com/ai/img_res/c88b5cb0f273a013c0a6ff4bf9f39892.jpg', '2025-03-17 13:59:30', '2025-03-17 13:59:30', 0);
INSERT INTO `news_info` (`id`, `title`, `description`, `content`, `read_count`, `comments`, `publish_date`, `author`, `category`, `image`, `create_time`, `update_time`, `deleted`) VALUES (5, '传统文化进校园活动在全国推广', '让中华优秀传统文化焕发新活力', '<p>教育部近日启动\"传统文化进校园\"系列活动，通过开设传统文化课程、举办传统节日文化活动等形式，让学生近距离感受中华优秀传统文化的魅力。</p><p>专家表示，传统文化教育对于培养学生的文化自信和民族认同感具有重要作用，是立德树人的重要内容。</p>', '1678', '92', '2022-09-10', '文化报', '5', 'https://ai-public.mastergo.com/ai/img_res/c88b5cb0f273a013c0a6ff4bf9f39892.jpg', '2025-03-17 13:59:30', '2025-03-17 13:59:30', 0);
COMMIT;

-- ----------------------------
-- Table structure for news_read_record
-- ----------------------------
DROP TABLE IF EXISTS `news_read_record`;
CREATE TABLE `news_read_record` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '记录ID',
  `news_id` bigint(20) NOT NULL COMMENT '新闻ID',
  `user_id` bigint(20) NOT NULL COMMENT '用户ID',
  `read_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '阅读时间',
  `read_duration` int(11) DEFAULT '0' COMMENT '阅读时长(秒)',
  PRIMARY KEY (`id`),
  KEY `idx_news_id` (`news_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_read_time` (`read_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='新闻阅读记录表';

-- ----------------------------
-- Records of news_read_record
-- ----------------------------
BEGIN;
COMMIT;

-- ----------------------------
-- Table structure for question
-- ----------------------------
DROP TABLE IF EXISTS `question`;
CREATE TABLE `question` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `type` varchar(20) NOT NULL COMMENT '题目类型（single-单选题，multiple-多选题，judge-判断题）',
  `category` varchar(64) DEFAULT NULL COMMENT '分类',
  `question` text NOT NULL COMMENT '题目内容',
  `options` json DEFAULT NULL COMMENT '选项，JSON格式',
  `correct_answer` varchar(255) NOT NULL COMMENT '正确答案，多选题为JSON数组',
  `explanation` text COMMENT '解析',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `deleted` tinyint(1) DEFAULT '0' COMMENT '是否删除',
  PRIMARY KEY (`id`),
  KEY `idx_category` (`category`)
) ENGINE=InnoDB AUTO_INCREMENT=11 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='题目表';

-- ----------------------------
-- Records of question
-- ----------------------------
BEGIN;
INSERT INTO `question` (`id`, `type`, `category`, `question`, `options`, `correct_answer`, `explanation`, `create_time`, `update_time`, `deleted`) VALUES (1, 'single', 'basic', '下列哪项不是我国的基本国策？', '[{\"id\": \"A\", \"text\": \"计划生育\"}, {\"id\": \"B\", \"text\": \"男女平等\"}, {\"id\": \"C\", \"text\": \"保护环境\"}, {\"id\": \"D\", \"text\": \"全民创业\"}]', 'D', '我国的基本国策包括计划生育、男女平等、保护环境、节约资源等，全民创业不是基本国策。', '2025-03-17 13:59:30', '2025-03-17 13:59:30', 0);
INSERT INTO `question` (`id`, `type`, `category`, `question`, `options`, `correct_answer`, `explanation`, `create_time`, `update_time`, `deleted`) VALUES (2, 'single', 'law', '我国《宪法》规定，中华人民共和国的一切权力属于谁？', '[{\"id\": \"A\", \"text\": \"国家\"}, {\"id\": \"B\", \"text\": \"人民\"}, {\"id\": \"C\", \"text\": \"政府\"}, {\"id\": \"D\", \"text\": \"党\"}]', 'B', '《宪法》第二条规定：中华人民共和国的一切权力属于人民。', '2025-03-17 13:59:30', '2025-03-17 13:59:30', 0);
INSERT INTO `question` (`id`, `type`, `category`, `question`, `options`, `correct_answer`, `explanation`, `create_time`, `update_time`, `deleted`) VALUES (3, 'multiple', 'safety', '以下哪些是安全出行的注意事项？', '[{\"id\": \"A\", \"text\": \"遵守交通规则\"}, {\"id\": \"B\", \"text\": \"不闯红灯\"}, {\"id\": \"C\", \"text\": \"不疲劳驾驶\"}, {\"id\": \"D\", \"text\": \"开车时可以接打电话\"}]', '[\"A\",\"B\",\"C\"]', '安全出行应当遵守交通规则，不闯红灯，不疲劳驾驶，开车时不应该接打电话，应使用蓝牙或免提设备。', '2025-03-17 13:59:30', '2025-03-17 13:59:30', 0);
INSERT INTO `question` (`id`, `type`, `category`, `question`, `options`, `correct_answer`, `explanation`, `create_time`, `update_time`, `deleted`) VALUES (4, 'judge', 'environment', '垃圾分类有助于环境保护和资源回收利用。', '[{\"id\": \"A\", \"text\": \"正确\"}, {\"id\": \"B\", \"text\": \"错误\"}]', 'A', '垃圾分类可以减少垃圾处理量，提高资源回收利用率，减少环境污染，是环保的重要措施。', '2025-03-17 13:59:30', '2025-03-17 13:59:30', 0);
INSERT INTO `question` (`id`, `type`, `category`, `question`, `options`, `correct_answer`, `explanation`, `create_time`, `update_time`, `deleted`) VALUES (5, 'single', 'culture', '中国的传统节日\"端午节\"是为了纪念谁？', '[{\"id\": \"A\", \"text\": \"孔子\"}, {\"id\": \"B\", \"text\": \"屈原\"}, {\"id\": \"C\", \"text\": \"关羽\"}, {\"id\": \"D\", \"text\": \"岳飞\"}]', 'B', '端午节是为了纪念战国时期楚国诗人屈原，他在五月初五日跳汨罗江自尽，后人为了纪念他，在这一天划龙舟、吃粽子。', '2025-03-17 13:59:30', '2025-03-17 13:59:30', 0);
INSERT INTO `question` (`id`, `type`, `category`, `question`, `options`, `correct_answer`, `explanation`, `create_time`, `update_time`, `deleted`) VALUES (6, 'multiple', 'basic', '以下哪些是中国的四大发明？', '[{\"id\": \"A\", \"text\": \"指南针\"}, {\"id\": \"B\", \"text\": \"火药\"}, {\"id\": \"C\", \"text\": \"造纸术\"}, {\"id\": \"D\", \"text\": \"印刷术\"}, {\"id\": \"E\", \"text\": \"蒸汽机\"}]', '[\"A\",\"B\",\"C\",\"D\"]', '中国古代四大发明是指南针、火药、造纸术和印刷术，蒸汽机是英国人瓦特改良的发明。', '2025-03-17 13:59:30', '2025-03-17 13:59:30', 0);
INSERT INTO `question` (`id`, `type`, `category`, `question`, `options`, `correct_answer`, `explanation`, `create_time`, `update_time`, `deleted`) VALUES (7, 'judge', 'law', '在中国，法定结婚年龄男性为22周岁，女性为20周岁。', '[{\"id\": \"B\", \"text\": \"正确\"}, {\"id\": \"A\", \"text\": \"错误\"}]', 'B', '根据《中华人民共和国民法典》规定，法定结婚年龄男性为22周岁，女性为20周岁。', '2025-03-17 13:59:30', '2025-03-17 13:59:30', 0);
INSERT INTO `question` (`id`, `type`, `category`, `question`, `options`, `correct_answer`, `explanation`, `create_time`, `update_time`, `deleted`) VALUES (8, 'single', 'safety', '发生火灾时，以下哪种做法是错误的？', '[{\"id\": \"A\", \"text\": \"用湿毛巾捂住口鼻\"}, {\"id\": \"B\", \"text\": \"弯腰或匍匐前进\"}, {\"id\": \"C\", \"text\": \"乘坐电梯逃生\"}, {\"id\": \"D\", \"text\": \"沿着安全出口疏散\"}]', 'C', '火灾发生时，电梯可能会因为断电或者烟雾进入而导致故障，被困在电梯内非常危险，应该走安全楼梯逃生。', '2025-03-17 13:59:30', '2025-03-17 13:59:30', 0);
INSERT INTO `question` (`id`, `type`, `category`, `question`, `options`, `correct_answer`, `explanation`, `create_time`, `update_time`, `deleted`) VALUES (9, 'multiple', 'environment', '以下哪些行为有助于节约能源？', '[{\"id\": \"A\", \"text\": \"随手关灯\"}, {\"id\": \"B\", \"text\": \"使用节能电器\"}, {\"id\": \"C\", \"text\": \"减少使用一次性物品\"}, {\"id\": \"D\", \"text\": \"长时间开着水龙头\"}]', '[\"A\",\"B\",\"C\"]', '节约能源的行为包括随手关灯、使用节能电器、减少使用一次性物品等，而长时间开着水龙头会浪费水资源。', '2025-03-17 13:59:30', '2025-03-17 13:59:30', 0);
INSERT INTO `question` (`id`, `type`, `category`, `question`, `options`, `correct_answer`, `explanation`, `create_time`, `update_time`, `deleted`) VALUES (10, 'single', 'culture', '中国传统文化中\"仁义礼智信\"五常思想出自哪家学派？', '[{\"id\": \"A\", \"text\": \"道家\"}, {\"id\": \"B\", \"text\": \"法家\"}, {\"id\": \"C\", \"text\": \"儒家\"}, {\"id\": \"D\", \"text\": \"墨家\"}]', 'C', '\"仁义礼智信\"是儒家的核心思想，强调个人道德修养和社会伦理规范。', '2025-03-17 13:59:30', '2025-03-17 13:59:30', 0);
COMMIT;

-- ----------------------------
-- Table structure for question_category
-- ----------------------------
DROP TABLE IF EXISTS `question_category`;
CREATE TABLE `question_category` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '分类ID',
  `name` varchar(50) NOT NULL COMMENT '分类名称',
  `description` varchar(255) DEFAULT NULL COMMENT '分类描述',
  `icon` varchar(255) DEFAULT NULL COMMENT '分类图标',
  `parent_id` bigint(20) DEFAULT '0' COMMENT '父分类ID',
  `sort` int(11) DEFAULT '0' COMMENT '排序',
  `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态：0-禁用，1-启用',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否删除：0-未删除，1-已删除',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='题目分类表';

-- ----------------------------
-- Records of question_category
-- ----------------------------
BEGIN;
COMMIT;

-- ----------------------------
-- Table structure for question_option
-- ----------------------------
DROP TABLE IF EXISTS `question_option`;
CREATE TABLE `question_option` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '选项ID',
  `question_id` bigint(20) NOT NULL COMMENT '题目ID',
  `option_code` varchar(10) NOT NULL COMMENT '选项编码',
  `option_content` text NOT NULL COMMENT '选项内容',
  `option_image` varchar(255) DEFAULT NULL COMMENT '选项图片',
  `sort` int(11) DEFAULT '0' COMMENT '排序',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_question_id` (`question_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='题目选项表';

-- ----------------------------
-- Records of question_option
-- ----------------------------
BEGIN;
COMMIT;

-- ----------------------------
-- Table structure for quiz_history
-- ----------------------------
DROP TABLE IF EXISTS `quiz_history`;
CREATE TABLE `quiz_history` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `open_id` varchar(64) NOT NULL COMMENT '用户OpenID',
  `score` int(11) DEFAULT '0' COMMENT '得分',
  `total` int(11) DEFAULT '0' COMMENT '总题数',
  `mode` varchar(20) DEFAULT 'practice' COMMENT '模式（practice-练习模式）',
  `mode_name` varchar(64) DEFAULT NULL COMMENT '模式名称',
  `timestamp` bigint(20) DEFAULT NULL COMMENT '答题时间戳',
  `time_str` varchar(20) DEFAULT NULL COMMENT '答题时间字符串',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `deleted` tinyint(1) DEFAULT '0' COMMENT '是否删除',
  PRIMARY KEY (`id`),
  KEY `idx_open_id` (`open_id`),
  KEY `idx_mode` (`mode`)
) ENGINE=InnoDB AUTO_INCREMENT=6 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='答题历史记录表';

-- ----------------------------
-- Records of quiz_history
-- ----------------------------
BEGIN;
INSERT INTO `quiz_history` (`id`, `open_id`, `score`, `total`, `mode`, `mode_name`, `timestamp`, `time_str`, `create_time`, `update_time`, `deleted`) VALUES (1, 'oXy1Z5Pu7QgLJqvL1234abcd', 80, 10, 'practice', '基础知识练习', 1672531200000, '2023-01-01 10:00', '2025-03-17 13:59:30', '2025-03-17 13:59:30', 0);
INSERT INTO `quiz_history` (`id`, `open_id`, `score`, `total`, `mode`, `mode_name`, `timestamp`, `time_str`, `create_time`, `update_time`, `deleted`) VALUES (2, 'oXy1Z5Pu7QgLJqvL1234abcd', 90, 10, 'practice', '法律法规练习', 1672617600000, '2023-01-02 10:00', '2025-03-17 13:59:30', '2025-03-17 13:59:30', 0);
INSERT INTO `quiz_history` (`id`, `open_id`, `score`, `total`, `mode`, `mode_name`, `timestamp`, `time_str`, `create_time`, `update_time`, `deleted`) VALUES (3, 'oXy1Z5Pu7QgLJqvL5678efgh', 70, 10, 'practice', '安全常识练习', 1672704000000, '2023-01-03 10:00', '2025-03-17 13:59:30', '2025-03-17 13:59:30', 0);
INSERT INTO `quiz_history` (`id`, `open_id`, `score`, `total`, `mode`, `mode_name`, `timestamp`, `time_str`, `create_time`, `update_time`, `deleted`) VALUES (4, 'oXy1Z5Pu7QgLJqvL9012ijkl', 60, 10, 'practice', '环保知识练习', 1672790400000, '2023-01-04 10:00', '2025-03-17 13:59:30', '2025-03-17 13:59:30', 0);
INSERT INTO `quiz_history` (`id`, `open_id`, `score`, `total`, `mode`, `mode_name`, `timestamp`, `time_str`, `create_time`, `update_time`, `deleted`) VALUES (5, 'oXy1Z5Pu7QgLJqvL3456mnop', 85, 10, 'practice', '文化素养练习', 1672876800000, '2023-01-05 10:00', '2025-03-17 13:59:30', '2025-03-17 13:59:30', 0);
COMMIT;

-- ----------------------------
-- Table structure for quiz_stats
-- ----------------------------
DROP TABLE IF EXISTS `quiz_stats`;
CREATE TABLE `quiz_stats` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `open_id` varchar(64) NOT NULL COMMENT '用户OpenID',
  `total_questions` int(11) DEFAULT '0' COMMENT '总题数',
  `correct_count` int(11) DEFAULT '0' COMMENT '正确题数',
  `wrong_count` int(11) DEFAULT '0' COMMENT '错误题数',
  `average_score` int(11) DEFAULT '0' COMMENT '平均分',
  `total_time` int(11) DEFAULT '0' COMMENT '总用时（秒）',
  `favorite_count` int(11) DEFAULT '0' COMMENT '收藏题数',
  `wrong_question_count` int(11) DEFAULT '0' COMMENT '错题数',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `deleted` tinyint(1) DEFAULT '0' COMMENT '是否删除',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_open_id` (`open_id`)
) ENGINE=InnoDB AUTO_INCREMENT=8 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='答题统计表';

-- ----------------------------
-- Records of quiz_stats
-- ----------------------------
BEGIN;
INSERT INTO `quiz_stats` (`id`, `open_id`, `total_questions`, `correct_count`, `wrong_count`, `average_score`, `total_time`, `favorite_count`, `wrong_question_count`, `create_time`, `update_time`, `deleted`) VALUES (1, 'oXy1Z5Pu7QgLJqvL1234abcd', 20, 17, 3, 85, 1800, 2, 2, '2025-03-17 13:59:30', '2025-03-17 13:59:30', 0);
INSERT INTO `quiz_stats` (`id`, `open_id`, `total_questions`, `correct_count`, `wrong_count`, `average_score`, `total_time`, `favorite_count`, `wrong_question_count`, `create_time`, `update_time`, `deleted`) VALUES (2, 'oXy1Z5Pu7QgLJqvL5678efgh', 10, 7, 3, 70, 900, 2, 1, '2025-03-17 13:59:30', '2025-03-17 13:59:30', 0);
INSERT INTO `quiz_stats` (`id`, `open_id`, `total_questions`, `correct_count`, `wrong_count`, `average_score`, `total_time`, `favorite_count`, `wrong_question_count`, `create_time`, `update_time`, `deleted`) VALUES (3, 'oXy1Z5Pu7QgLJqvL9012ijkl', 10, 6, 4, 60, 1200, 1, 1, '2025-03-17 13:59:30', '2025-03-17 13:59:30', 0);
INSERT INTO `quiz_stats` (`id`, `open_id`, `total_questions`, `correct_count`, `wrong_count`, `average_score`, `total_time`, `favorite_count`, `wrong_question_count`, `create_time`, `update_time`, `deleted`) VALUES (4, 'oXy1Z5Pu7QgLJqvL3456mnop', 10, 8, 2, 80, 1000, 0, 1, '2025-03-17 13:59:30', '2025-03-17 13:59:30', 0);
INSERT INTO `quiz_stats` (`id`, `open_id`, `total_questions`, `correct_count`, `wrong_count`, `average_score`, `total_time`, `favorite_count`, `wrong_question_count`, `create_time`, `update_time`, `deleted`) VALUES (5, 'oXy1Z5Pu7QgLJqvL7890qrst', 0, 0, 0, 0, 0, 0, 0, '2025-03-17 13:59:30', '2025-03-17 13:59:30', 0);
INSERT INTO `quiz_stats` (`id`, `open_id`, `total_questions`, `correct_count`, `wrong_count`, `average_score`, `total_time`, `favorite_count`, `wrong_question_count`, `create_time`, `update_time`, `deleted`) VALUES (7, 'o0bV45Gg3kUWKkn33ZHGO8D_SpO0', 4, 0, 4, 0, 0, 0, 4, '2025-04-01 09:00:15', '2025-04-01 11:18:06', 0);
COMMIT;

-- ----------------------------
-- Table structure for sys_admin
-- ----------------------------
DROP TABLE IF EXISTS `sys_admin`;
CREATE TABLE `sys_admin` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '管理员ID',
  `username` varchar(50) NOT NULL COMMENT '用户名',
  `password` varchar(100) NOT NULL COMMENT '密码',
  `real_name` varchar(50) DEFAULT NULL COMMENT '真实姓名',
  `avatar` varchar(255) DEFAULT NULL COMMENT '头像',
  `email` varchar(100) DEFAULT NULL COMMENT '邮箱',
  `phone` varchar(20) DEFAULT NULL COMMENT '手机号',
  `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态：0-禁用，1-启用',
  `last_login_time` datetime DEFAULT NULL COMMENT '最后登录时间',
  `last_login_ip` varchar(50) DEFAULT NULL COMMENT '最后登录IP',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否删除：0-未删除，1-已删除',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_username` (`username`)
) ENGINE=InnoDB AUTO_INCREMENT=106 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='管理员表';

-- ----------------------------
-- Records of sys_admin
-- ----------------------------
BEGIN;
INSERT INTO `sys_admin` (`id`, `username`, `password`, `real_name`, `avatar`, `email`, `phone`, `status`, `last_login_time`, `last_login_ip`, `create_time`, `update_time`, `deleted`) VALUES (1, 'admin', '123456', '系统管理员', NULL, NULL, NULL, 1, NULL, NULL, '2025-04-08 13:48:57', '2025-04-08 14:00:53', 0);
COMMIT;

-- ----------------------------
-- Table structure for sys_admin_role
-- ----------------------------
DROP TABLE IF EXISTS `sys_admin_role`;
CREATE TABLE `sys_admin_role` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `admin_id` bigint(20) NOT NULL COMMENT '管理员ID',
  `role_id` bigint(20) NOT NULL COMMENT '角色ID',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_admin_role` (`admin_id`,`role_id`)
) ENGINE=InnoDB AUTO_INCREMENT=106 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='管理员角色关联表';

-- ----------------------------
-- Records of sys_admin_role
-- ----------------------------
BEGIN;
INSERT INTO `sys_admin_role` (`id`, `admin_id`, `role_id`, `create_time`) VALUES (1, 1, 1, '2025-04-08 13:48:57');
COMMIT;

-- ----------------------------
-- Table structure for sys_config
-- ----------------------------
DROP TABLE IF EXISTS `sys_config`;
CREATE TABLE `sys_config` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '配置ID',
  `config_key` varchar(50) NOT NULL COMMENT '配置键',
  `config_value` text NOT NULL COMMENT '配置值',
  `config_type` varchar(20) DEFAULT 'STRING' COMMENT '配置类型',
  `description` varchar(255) DEFAULT NULL COMMENT '配置描述',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_config_key` (`config_key`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='系统配置表';

-- ----------------------------
-- Records of sys_config
-- ----------------------------
BEGIN;
COMMIT;

-- ----------------------------
-- Table structure for sys_operation_log
-- ----------------------------
DROP TABLE IF EXISTS `sys_operation_log`;
CREATE TABLE `sys_operation_log` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '日志ID',
  `admin_id` bigint(20) DEFAULT NULL COMMENT '管理员ID',
  `admin_name` varchar(50) DEFAULT NULL COMMENT '管理员姓名',
  `operation_type` varchar(50) NOT NULL COMMENT '操作类型',
  `operation_method` varchar(255) NOT NULL COMMENT '操作方法',
  `operation_params` text COMMENT '操作参数',
  `operation_result` text COMMENT '操作结果',
  `status` tinyint(1) NOT NULL COMMENT '状态：0-失败，1-成功',
  `error_msg` text COMMENT '错误信息',
  `operation_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '操作时间',
  `operation_ip` varchar(50) DEFAULT NULL COMMENT '操作IP',
  PRIMARY KEY (`id`),
  KEY `idx_admin_id` (`admin_id`),
  KEY `idx_operation_time` (`operation_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='操作日志表';

-- ----------------------------
-- Records of sys_operation_log
-- ----------------------------
BEGIN;
COMMIT;

-- ----------------------------
-- Table structure for sys_permission
-- ----------------------------
DROP TABLE IF EXISTS `sys_permission`;
CREATE TABLE `sys_permission` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '权限ID',
  `parent_id` bigint(20) DEFAULT '0' COMMENT '父权限ID',
  `name` varchar(50) NOT NULL COMMENT '权限名称',
  `code` varchar(50) NOT NULL COMMENT '权限编码',
  `type` tinyint(1) NOT NULL COMMENT '权限类型：1-菜单，2-按钮',
  `path` varchar(100) DEFAULT NULL COMMENT '路由路径',
  `component` varchar(100) DEFAULT NULL COMMENT '组件路径',
  `icon` varchar(50) DEFAULT NULL COMMENT '图标',
  `sort` int(11) DEFAULT '0' COMMENT '排序',
  `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态：0-禁用，1-启用',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否删除：0-未删除，1-已删除',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_code` (`code`)
) ENGINE=InnoDB AUTO_INCREMENT=819 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='权限表';

-- ----------------------------
-- Records of sys_permission
-- ----------------------------
BEGIN;
INSERT INTO `sys_permission` (`id`, `parent_id`, `name`, `code`, `type`, `path`, `component`, `icon`, `sort`, `status`, `create_time`, `update_time`, `deleted`) VALUES (1, 0, '系统管理', 'system', 1, '/system', 'Layout', 'el-icon-setting', 1, 1, '2025-04-08 13:54:12', '2025-04-08 13:54:12', 0);
INSERT INTO `sys_permission` (`id`, `parent_id`, `name`, `code`, `type`, `path`, `component`, `icon`, `sort`, `status`, `create_time`, `update_time`, `deleted`) VALUES (2, 1, '用户管理', 'system:admin', 1, 'admin', 'system/admin/index', 'el-icon-user', 1, 1, '2025-04-08 13:54:12', '2025-04-08 13:54:12', 0);
INSERT INTO `sys_permission` (`id`, `parent_id`, `name`, `code`, `type`, `path`, `component`, `icon`, `sort`, `status`, `create_time`, `update_time`, `deleted`) VALUES (3, 1, '角色管理', 'system:role', 1, 'role', 'system/role/index', 'el-icon-s-check', 2, 1, '2025-04-08 13:54:12', '2025-04-08 13:54:12', 0);
INSERT INTO `sys_permission` (`id`, `parent_id`, `name`, `code`, `type`, `path`, `component`, `icon`, `sort`, `status`, `create_time`, `update_time`, `deleted`) VALUES (4, 1, '权限管理', 'system:permission', 1, 'permission', 'system/permission/index', 'el-icon-key', 3, 1, '2025-04-08 13:54:12', '2025-04-08 13:54:12', 0);
INSERT INTO `sys_permission` (`id`, `parent_id`, `name`, `code`, `type`, `path`, `component`, `icon`, `sort`, `status`, `create_time`, `update_time`, `deleted`) VALUES (5, 2, '用户列表', 'system:admin:list', 2, NULL, NULL, NULL, 1, 1, '2025-04-08 13:54:12', '2025-04-08 13:54:12', 0);
INSERT INTO `sys_permission` (`id`, `parent_id`, `name`, `code`, `type`, `path`, `component`, `icon`, `sort`, `status`, `create_time`, `update_time`, `deleted`) VALUES (6, 2, '添加用户', 'system:admin:add', 2, NULL, NULL, NULL, 2, 1, '2025-04-08 13:54:12', '2025-04-08 13:54:12', 0);
INSERT INTO `sys_permission` (`id`, `parent_id`, `name`, `code`, `type`, `path`, `component`, `icon`, `sort`, `status`, `create_time`, `update_time`, `deleted`) VALUES (7, 2, '编辑用户', 'system:admin:edit', 2, NULL, NULL, NULL, 3, 1, '2025-04-08 13:54:12', '2025-04-08 13:54:12', 0);
INSERT INTO `sys_permission` (`id`, `parent_id`, `name`, `code`, `type`, `path`, `component`, `icon`, `sort`, `status`, `create_time`, `update_time`, `deleted`) VALUES (8, 2, '删除用户', 'system:admin:delete', 2, NULL, NULL, NULL, 4, 1, '2025-04-08 13:54:12', '2025-04-08 13:54:12', 0);
INSERT INTO `sys_permission` (`id`, `parent_id`, `name`, `code`, `type`, `path`, `component`, `icon`, `sort`, `status`, `create_time`, `update_time`, `deleted`) VALUES (9, 3, '角色列表', 'system:role:list', 2, NULL, NULL, NULL, 1, 1, '2025-04-08 13:54:12', '2025-04-08 13:54:12', 0);
INSERT INTO `sys_permission` (`id`, `parent_id`, `name`, `code`, `type`, `path`, `component`, `icon`, `sort`, `status`, `create_time`, `update_time`, `deleted`) VALUES (10, 3, '添加角色', 'system:role:add', 2, NULL, NULL, NULL, 2, 1, '2025-04-08 13:54:12', '2025-04-08 13:54:12', 0);
INSERT INTO `sys_permission` (`id`, `parent_id`, `name`, `code`, `type`, `path`, `component`, `icon`, `sort`, `status`, `create_time`, `update_time`, `deleted`) VALUES (11, 3, '编辑角色', 'system:role:edit', 2, NULL, NULL, NULL, 3, 1, '2025-04-08 13:54:12', '2025-04-08 13:54:12', 0);
INSERT INTO `sys_permission` (`id`, `parent_id`, `name`, `code`, `type`, `path`, `component`, `icon`, `sort`, `status`, `create_time`, `update_time`, `deleted`) VALUES (12, 3, '删除角色', 'system:role:delete', 2, NULL, NULL, NULL, 4, 1, '2025-04-08 13:54:12', '2025-04-08 13:54:12', 0);
INSERT INTO `sys_permission` (`id`, `parent_id`, `name`, `code`, `type`, `path`, `component`, `icon`, `sort`, `status`, `create_time`, `update_time`, `deleted`) VALUES (13, 4, '权限列表', 'system:permission:list', 2, NULL, NULL, NULL, 1, 1, '2025-04-08 13:54:12', '2025-04-08 13:54:12', 0);
INSERT INTO `sys_permission` (`id`, `parent_id`, `name`, `code`, `type`, `path`, `component`, `icon`, `sort`, `status`, `create_time`, `update_time`, `deleted`) VALUES (14, 4, '添加权限', 'system:permission:add', 2, NULL, NULL, NULL, 2, 1, '2025-04-08 13:54:12', '2025-04-08 13:54:12', 0);
INSERT INTO `sys_permission` (`id`, `parent_id`, `name`, `code`, `type`, `path`, `component`, `icon`, `sort`, `status`, `create_time`, `update_time`, `deleted`) VALUES (15, 4, '编辑权限', 'system:permission:edit', 2, NULL, NULL, NULL, 3, 1, '2025-04-08 13:54:12', '2025-04-08 13:54:12', 0);
INSERT INTO `sys_permission` (`id`, `parent_id`, `name`, `code`, `type`, `path`, `component`, `icon`, `sort`, `status`, `create_time`, `update_time`, `deleted`) VALUES (16, 4, '删除权限', 'system:permission:delete', 2, NULL, NULL, NULL, 4, 1, '2025-04-08 13:54:12', '2025-04-08 13:54:12', 0);
COMMIT;

-- ----------------------------
-- Table structure for sys_role
-- ----------------------------
DROP TABLE IF EXISTS `sys_role`;
CREATE TABLE `sys_role` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '角色ID',
  `role_name` varchar(50) NOT NULL COMMENT '角色名称',
  `role_code` varchar(50) NOT NULL COMMENT '角色编码',
  `description` varchar(255) DEFAULT NULL COMMENT '角色描述',
  `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态：0-禁用，1-启用',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否删除：0-未删除，1-已删除',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_role_code` (`role_code`)
) ENGINE=InnoDB AUTO_INCREMENT=107 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='角色表';

-- ----------------------------
-- Records of sys_role
-- ----------------------------
BEGIN;
INSERT INTO `sys_role` (`id`, `role_name`, `role_code`, `description`, `status`, `create_time`, `update_time`, `deleted`) VALUES (1, '超级管理员', 'ROLE_SUPER_ADMIN', '拥有所有权限的超级管理员', 1, '2025-04-08 13:48:57', '2025-04-08 13:48:57', 0);
INSERT INTO `sys_role` (`id`, `role_name`, `role_code`, `description`, `status`, `create_time`, `update_time`, `deleted`) VALUES (2, '内容管理员', 'ROLE_CONTENT_ADMIN', '负责内容管理的管理员', 1, '2025-04-08 13:48:57', '2025-04-08 13:48:57', 0);
COMMIT;

-- ----------------------------
-- Table structure for sys_role_permission
-- ----------------------------
DROP TABLE IF EXISTS `sys_role_permission`;
CREATE TABLE `sys_role_permission` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `role_id` bigint(20) NOT NULL COMMENT '角色ID',
  `permission_id` bigint(20) NOT NULL COMMENT '权限ID',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_role_permission` (`role_id`,`permission_id`)
) ENGINE=InnoDB AUTO_INCREMENT=58 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='角色权限关联表';

-- ----------------------------
-- Records of sys_role_permission
-- ----------------------------
BEGIN;
INSERT INTO `sys_role_permission` (`id`, `role_id`, `permission_id`, `create_time`) VALUES (1, 1, 1, '2025-04-08 13:54:12');
INSERT INTO `sys_role_permission` (`id`, `role_id`, `permission_id`, `create_time`) VALUES (2, 1, 2, '2025-04-08 13:54:12');
INSERT INTO `sys_role_permission` (`id`, `role_id`, `permission_id`, `create_time`) VALUES (3, 1, 3, '2025-04-08 13:54:12');
INSERT INTO `sys_role_permission` (`id`, `role_id`, `permission_id`, `create_time`) VALUES (4, 1, 4, '2025-04-08 13:54:12');
INSERT INTO `sys_role_permission` (`id`, `role_id`, `permission_id`, `create_time`) VALUES (5, 1, 5, '2025-04-08 13:54:12');
INSERT INTO `sys_role_permission` (`id`, `role_id`, `permission_id`, `create_time`) VALUES (6, 1, 6, '2025-04-08 13:54:12');
INSERT INTO `sys_role_permission` (`id`, `role_id`, `permission_id`, `create_time`) VALUES (7, 1, 7, '2025-04-08 13:54:12');
INSERT INTO `sys_role_permission` (`id`, `role_id`, `permission_id`, `create_time`) VALUES (8, 1, 8, '2025-04-08 13:54:12');
INSERT INTO `sys_role_permission` (`id`, `role_id`, `permission_id`, `create_time`) VALUES (9, 1, 9, '2025-04-08 13:54:12');
INSERT INTO `sys_role_permission` (`id`, `role_id`, `permission_id`, `create_time`) VALUES (10, 1, 10, '2025-04-08 13:54:12');
INSERT INTO `sys_role_permission` (`id`, `role_id`, `permission_id`, `create_time`) VALUES (11, 1, 11, '2025-04-08 13:54:12');
INSERT INTO `sys_role_permission` (`id`, `role_id`, `permission_id`, `create_time`) VALUES (12, 1, 12, '2025-04-08 13:54:12');
INSERT INTO `sys_role_permission` (`id`, `role_id`, `permission_id`, `create_time`) VALUES (13, 1, 13, '2025-04-08 13:54:12');
INSERT INTO `sys_role_permission` (`id`, `role_id`, `permission_id`, `create_time`) VALUES (14, 1, 14, '2025-04-08 13:54:12');
INSERT INTO `sys_role_permission` (`id`, `role_id`, `permission_id`, `create_time`) VALUES (15, 1, 15, '2025-04-08 13:54:12');
INSERT INTO `sys_role_permission` (`id`, `role_id`, `permission_id`, `create_time`) VALUES (16, 1, 16, '2025-04-08 13:54:12');
INSERT INTO `sys_role_permission` (`id`, `role_id`, `permission_id`, `create_time`) VALUES (17, 2, 1, '2025-04-08 13:54:12');
INSERT INTO `sys_role_permission` (`id`, `role_id`, `permission_id`, `create_time`) VALUES (18, 2, 2, '2025-04-08 13:54:12');
INSERT INTO `sys_role_permission` (`id`, `role_id`, `permission_id`, `create_time`) VALUES (19, 2, 5, '2025-04-08 13:54:12');
INSERT INTO `sys_role_permission` (`id`, `role_id`, `permission_id`, `create_time`) VALUES (20, 2, 7, '2025-04-08 13:54:12');
COMMIT;

-- ----------------------------
-- Table structure for user
-- ----------------------------
DROP TABLE IF EXISTS `user`;
CREATE TABLE `user` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `open_id` varchar(64) DEFAULT NULL COMMENT '微信OpenID',
  `nickname` varchar(64) DEFAULT NULL COMMENT '昵称',
  `avatar` varchar(255) DEFAULT NULL COMMENT '头像',
  `gender` tinyint(4) DEFAULT '0' COMMENT '性别（0-未知，1-男，2-女）',
  `phone` varchar(20) DEFAULT NULL COMMENT '手机号',
  `last_login_time` datetime DEFAULT NULL COMMENT '最后登录时间',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `deleted` tinyint(1) DEFAULT '0' COMMENT '是否删除',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_open_id` (`open_id`)
) ENGINE=InnoDB AUTO_INCREMENT=15 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='用户表';

-- ----------------------------
-- Records of user
-- ----------------------------
BEGIN;
INSERT INTO `user` (`id`, `open_id`, `nickname`, `avatar`, `gender`, `phone`, `last_login_time`, `create_time`, `update_time`, `deleted`) VALUES (14, 'o0bV45Gg3kUWKkn33ZHGO8D_SpO0', NULL, NULL, 0, '15232342020', '2025-04-01 15:56:10', '2025-03-31 11:35:35', '2025-04-01 16:45:56', 0);
COMMIT;

-- ----------------------------
-- Table structure for user_answer_record
-- ----------------------------
DROP TABLE IF EXISTS `user_answer_record`;
CREATE TABLE `user_answer_record` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '记录ID',
  `user_id` bigint(20) NOT NULL COMMENT '用户ID',
  `question_id` bigint(20) NOT NULL COMMENT '题目ID',
  `user_answer` varchar(255) NOT NULL COMMENT '用户答案',
  `is_correct` tinyint(1) NOT NULL COMMENT '是否正确：0-错误，1-正确',
  `answer_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '答题时间',
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_question_id` (`question_id`),
  KEY `idx_answer_time` (`answer_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='用户答题记录表';

-- ----------------------------
-- Records of user_answer_record
-- ----------------------------
BEGIN;
COMMIT;

-- ----------------------------
-- Table structure for wrong_question
-- ----------------------------
DROP TABLE IF EXISTS `wrong_question`;
CREATE TABLE `wrong_question` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `open_id` varchar(64) NOT NULL COMMENT '用户OpenID',
  `question_id` bigint(20) NOT NULL COMMENT '题目ID',
  `user_answer` varchar(255) DEFAULT NULL COMMENT '用户答案',
  `wrong_count` int(11) DEFAULT '1' COMMENT '错误次数',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `deleted` tinyint(1) DEFAULT '0' COMMENT '是否删除',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_user_question` (`open_id`,`question_id`),
  KEY `idx_open_id` (`open_id`),
  KEY `idx_question_id` (`question_id`)
) ENGINE=InnoDB AUTO_INCREMENT=11 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='错题记录表';

-- ----------------------------
-- Records of wrong_question
-- ----------------------------
BEGIN;
INSERT INTO `wrong_question` (`id`, `open_id`, `question_id`, `user_answer`, `wrong_count`, `create_time`, `update_time`, `deleted`) VALUES (8, 'o0bV45Gg3kUWKkn33ZHGO8D_SpO0', 1, 'A', 1, '2025-04-01 09:18:05', '2025-04-01 09:18:05', 0);
INSERT INTO `wrong_question` (`id`, `open_id`, `question_id`, `user_answer`, `wrong_count`, `create_time`, `update_time`, `deleted`) VALUES (9, 'o0bV45Gg3kUWKkn33ZHGO8D_SpO0', 3, 'D,C,B', 1, '2025-04-01 09:39:40', '2025-04-01 09:39:40', 0);
INSERT INTO `wrong_question` (`id`, `open_id`, `question_id`, `user_answer`, `wrong_count`, `create_time`, `update_time`, `deleted`) VALUES (10, 'o0bV45Gg3kUWKkn33ZHGO8D_SpO0', 4, 'B', 1, '2025-04-01 11:18:06', '2025-04-01 11:18:06', 0);
COMMIT;

SET FOREIGN_KEY_CHECKS = 1;
