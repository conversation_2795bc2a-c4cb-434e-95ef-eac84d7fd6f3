<template>
  <div class="charity-list">
    <el-card class="box-card">
      <template #header>
        <div class="card-header">
          <span>公益活动管理</span>
          <el-button type="primary" @click="handleCreate">
            <el-icon><Plus /></el-icon>
            新建活动
          </el-button>
        </div>
      </template>

      <!-- 搜索栏 -->
      <div class="search-bar">
        <el-form :inline="true" :model="searchForm" class="search-form">
          <el-form-item label="活动标题">
            <el-input v-model="searchForm.title" placeholder="请输入活动标题" clearable />
          </el-form-item>
          <el-form-item label="活动状态">
            <el-select v-model="searchForm.status" placeholder="请选择状态" clearable>
              <el-option label="即将开始" value="upcoming" />
              <el-option label="进行中" value="ongoing" />
              <el-option label="已结束" value="finished" />
            </el-select>
          </el-form-item>
          <el-form-item label="活动分类">
            <el-select v-model="searchForm.category" placeholder="请选择分类" clearable>
              <el-option label="环保公益" value="环保" />
              <el-option label="教育助学" value="教育" />
              <el-option label="扶贫济困" value="扶贫" />
              <el-option label="关爱老人" value="关爱" />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="handleSearch">搜索</el-button>
            <el-button @click="handleReset">重置</el-button>
          </el-form-item>
        </el-form>
      </div>

      <!-- 数据表格 -->
      <el-table :data="tableData" style="width: 100%" v-loading="loading">
        <el-table-column prop="id" label="ID" width="80" />
        <el-table-column prop="title" label="活动标题" min-width="200" />
        <el-table-column prop="category" label="分类" width="100" />
        <el-table-column prop="status" label="状态" width="100">
          <template #default="scope">
            <el-tag :type="getStatusType(scope.row.status)">
              {{ getStatusText(scope.row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="participants" label="参与人数" width="100">
          <template #default="scope">
            {{ scope.row.participants }}/{{ scope.row.max_participants }}
          </template>
        </el-table-column>
        <el-table-column prop="location" label="活动地点" min-width="150" />
        <el-table-column prop="time" label="活动时间" min-width="150" />
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="scope">
            <el-button size="small" @click="handleView(scope.row)">查看</el-button>
            <el-button size="small" type="primary" @click="handleEdit(scope.row)">编辑</el-button>
            <el-button size="small" type="danger" @click="handleDelete(scope.row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination">
        <el-pagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.size"
          :page-sizes="[10, 20, 50, 100]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 查看详情对话框 -->
    <el-dialog v-model="dialogVisible" title="活动详情" width="60%">
      <div v-if="currentItem">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="活动标题">{{ currentItem.title }}</el-descriptions-item>
          <el-descriptions-item label="活动分类">{{ currentItem.category }}</el-descriptions-item>
          <el-descriptions-item label="活动状态">
            <el-tag :type="getStatusType(currentItem.status)">
              {{ getStatusText(currentItem.status) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="参与人数">
            {{ currentItem.participants }}/{{ currentItem.max_participants }}
          </el-descriptions-item>
          <el-descriptions-item label="活动地点">{{ currentItem.location }}</el-descriptions-item>
          <el-descriptions-item label="活动时间">{{ currentItem.time }}</el-descriptions-item>
          <el-descriptions-item label="组织者">{{ currentItem.organizer }}</el-descriptions-item>
          <el-descriptions-item label="联系方式">{{ currentItem.contact }}</el-descriptions-item>
          <el-descriptions-item label="活动描述" :span="2">{{ currentItem.description }}</el-descriptions-item>
          <el-descriptions-item label="活动内容" :span="2">
            <div v-html="currentItem.content"></div>
          </el-descriptions-item>
        </el-descriptions>
      </div>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus } from '@element-plus/icons-vue'
import { getCharities, deleteCharity } from '@/api/charity'
import { useRouter } from 'vue-router'

const router = useRouter()

// 响应式数据
const loading = ref(false)
const tableData = ref([])
const dialogVisible = ref(false)
const currentItem = ref(null)

// 搜索表单
const searchForm = reactive({
  title: '',
  status: '',
  category: ''
})

// 分页
const pagination = reactive({
  page: 1,
  size: 10,
  total: 0
})

// 获取数据
const fetchData = async () => {
  loading.value = true
  try {
    const params = {
      skip: (pagination.page - 1) * pagination.size,
      limit: pagination.size,
      ...searchForm
    }
    const response = await getCharities(params)
    tableData.value = response || []
    // 注意：这里需要后端返回总数，暂时使用数据长度
    pagination.total = tableData.value.length
  } catch (error) {
    ElMessage.error('获取数据失败')
    console.error(error)
  } finally {
    loading.value = false
  }
}

// 状态相关方法
const getStatusType = (status: string) => {
  const statusMap = {
    upcoming: 'warning',
    ongoing: 'success',
    finished: 'info'
  }
  return statusMap[status] || 'info'
}

const getStatusText = (status: string) => {
  const statusMap = {
    upcoming: '即将开始',
    ongoing: '进行中',
    finished: '已结束'
  }
  return statusMap[status] || status
}

// 事件处理
const handleSearch = () => {
  pagination.page = 1
  fetchData()
}

const handleReset = () => {
  Object.keys(searchForm).forEach(key => {
    searchForm[key] = ''
  })
  pagination.page = 1
  fetchData()
}

const handleCreate = () => {
  router.push('/charity/create')
}

const handleView = (row: any) => {
  currentItem.value = row
  dialogVisible.value = true
}

const handleEdit = (row: any) => {
  router.push(`/charity/create?id=${row.id}`)
}

const handleDelete = async (row: any) => {
  try {
    await ElMessageBox.confirm('确定要删除这个活动吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    
    await deleteCharity(row.id)
    ElMessage.success('删除成功')
    fetchData()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败')
    }
  }
}

const handleSizeChange = (size: number) => {
  pagination.size = size
  pagination.page = 1
  fetchData()
}

const handleCurrentChange = (page: number) => {
  pagination.page = page
  fetchData()
}

// 初始化
onMounted(() => {
  fetchData()
})
</script>

<style scoped>
.charity-list {
  padding: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.search-bar {
  margin-bottom: 20px;
}

.search-form {
  background-color: #f5f5f5;
  padding: 20px;
  border-radius: 4px;
}

.pagination {
  margin-top: 20px;
  text-align: right;
}
</style>
