from typing import List, Optional
from sqlalchemy.orm import Session
from app.models.role import SysRole, SysPermission, SysRolePermission, SysUserRole
from app.schemas.role import RoleCreate, RoleUpdate, PermissionCreate, PermissionUpdate, RolePermissionAssign, UserRoleAssign

# 角色CRUD操作
def get_role(db: Session, role_id: int) -> Optional[SysRole]:
    return db.query(SysRole).filter(SysRole.id == role_id, SysRole.deleted == 0).first()

def get_role_by_code(db: Session, code: str) -> Optional[SysRole]:
    return db.query(SysRole).filter(SysRole.role_code == code, SysRole.deleted == 0).first()

def get_roles(db: Session, skip: int = 0, limit: int = 100) -> List[SysRole]:
    return db.query(SysRole).filter(SysRole.deleted == 0).offset(skip).limit(limit).all()

def create_role(db: Session, role: RoleCreate) -> SysRole:
    db_role = SysRole(**role.dict())
    db.add(db_role)
    db.commit()
    db.refresh(db_role)
    return db_role

def update_role(db: Session, role_id: int, role: RoleUpdate) -> Optional[SysRole]:
    db_role = get_role(db, role_id)
    if db_role:
        update_data = role.dict(exclude_unset=True)
        for field, value in update_data.items():
            setattr(db_role, field, value)
        db.commit()
        db.refresh(db_role)
    return db_role

def delete_role(db: Session, role_id: int) -> bool:
    db_role = get_role(db, role_id)
    if db_role:
        db_role.deleted = 1
        db.commit()
        return True
    return False

# 权限CRUD操作
def get_permission(db: Session, permission_id: int) -> Optional[SysPermission]:
    return db.query(SysPermission).filter(SysPermission.id == permission_id, SysPermission.deleted == 0).first()

def get_permission_by_code(db: Session, code: str) -> Optional[SysPermission]:
    return db.query(SysPermission).filter(SysPermission.code == code, SysPermission.deleted == 0).first()

def get_permissions(db: Session, skip: int = 0, limit: int = 100) -> List[SysPermission]:
    return db.query(SysPermission).filter(SysPermission.deleted == 0).order_by(SysPermission.sort).offset(skip).limit(limit).all()

def get_permissions_by_type(db: Session, permission_type: str) -> List[SysPermission]:
    return db.query(SysPermission).filter(SysPermission.type == permission_type, SysPermission.deleted == 0).order_by(SysPermission.sort).all()

def create_permission(db: Session, permission: PermissionCreate) -> SysPermission:
    db_permission = SysPermission(**permission.dict())
    db.add(db_permission)
    db.commit()
    db.refresh(db_permission)
    return db_permission

def update_permission(db: Session, permission_id: int, permission: PermissionUpdate) -> Optional[SysPermission]:
    db_permission = get_permission(db, permission_id)
    if db_permission:
        update_data = permission.dict(exclude_unset=True)
        for field, value in update_data.items():
            setattr(db_permission, field, value)
        db.commit()
        db.refresh(db_permission)
    return db_permission

def delete_permission(db: Session, permission_id: int) -> bool:
    db_permission = get_permission(db, permission_id)
    if db_permission:
        db_permission.deleted = 1
        db.commit()
        return True
    return False

# 角色权限关联操作
def get_role_permissions(db: Session, role_id: int) -> List[SysPermission]:
    return db.query(SysPermission).join(
        SysRolePermission, SysPermission.id == SysRolePermission.permission_id
    ).filter(
        SysRolePermission.role_id == role_id,
        SysPermission.deleted == 0
    ).all()

def assign_role_permissions(db: Session, role_permission_assign: RolePermissionAssign) -> bool:
    try:
        # 先删除原有的角色权限关联
        db.query(SysRolePermission).filter(SysRolePermission.role_id == role_permission_assign.role_id).delete()
        
        # 添加新的角色权限关联
        for permission_id in role_permission_assign.permission_ids:
            role_permission = SysRolePermission(
                role_id=role_permission_assign.role_id,
                permission_id=permission_id
            )
            db.add(role_permission)
        
        db.commit()
        return True
    except Exception:
        db.rollback()
        return False

# 用户角色关联操作
def get_user_roles(db: Session, user_id: int) -> List[SysRole]:
    return db.query(SysRole).join(
        SysUserRole, SysRole.id == SysUserRole.role_id
    ).filter(
        SysUserRole.user_id == user_id,
        SysRole.deleted == 0
    ).all()

def assign_user_roles(db: Session, user_role_assign: UserRoleAssign) -> bool:
    try:
        # 先删除原有的用户角色关联
        db.query(SysUserRole).filter(SysUserRole.user_id == user_role_assign.user_id).delete()
        
        # 添加新的用户角色关联
        for role_id in user_role_assign.role_ids:
            user_role = SysUserRole(
                user_id=user_role_assign.user_id,
                role_id=role_id
            )
            db.add(user_role)
        
        db.commit()
        return True
    except Exception:
        db.rollback()
        return False

def get_users_by_role(db: Session, role_id: int) -> List[int]:
    """获取拥有指定角色的用户ID列表"""
    user_roles = db.query(SysUserRole).filter(SysUserRole.role_id == role_id).all()
    return [ur.user_id for ur in user_roles]
