from sqlalchemy import Column, Integer, String, DateTime, func
from sqlalchemy.dialects.mysql import TINYINT  # 修正导入
from app.database import Base

class SysAdmin(Base):
    __tablename__ = "sys_admin"

    id = Column(Integer, primary_key=True, index=True)
    username = Column(String(50), unique=True, index=True, nullable=False)
    password = Column(String(100), nullable=False) # 数据库中存储的是哈希后的密码
    real_name = Column(String(50))
    avatar = Column(String(255))
    email = Column(String(100))
    phone = Column(String(20))
    status = Column(TINYINT, nullable=False, default=1)  # 修正类型
    last_login_time = Column(DateTime)
    last_login_ip = Column(String(50))
    create_time = Column(DateTime, default=func.now())
    update_time = Column(DateTime, default=func.now(), onupdate=func.now())
    deleted = Column(TINYINT, default=0)  # 修正类型